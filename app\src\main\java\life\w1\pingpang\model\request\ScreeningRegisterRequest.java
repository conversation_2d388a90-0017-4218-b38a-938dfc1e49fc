package life.w1.pingpang.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScreeningRegisterRequest {

    private String name;

    private Integer gender;

    private String age;

    private String idnum;

    private  String grade;

    private  String classes;

    private  String device_id;
}

package life.w1.pingpang.utils;


import static android.app.Activity.RESULT_OK;
import static androidx.core.app.ActivityCompat.startActivityForResult;
import static androidx.core.content.ContextCompat.startActivity;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.Fragment;
import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.widget.Toast;

import androidx.activity.ComponentActivity;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.core.content.FileProvider;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;
import okio.ForwardingSource;
import okio.Okio;
import okio.Source;

public class UpgradeHelper {

    protected Activity activity;
    public ActivityResultLauncher<Intent> installPermissionLauncher;
    private ProgressDialog progressDialog;



    public void checkForUpdate(Activity activity, ComponentActivity componentActivity) {

        this.activity = activity;

        this.installPermissionLauncher = componentActivity.registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
//                result -> {
//                    Toast.makeText(this.activity, "getresultCode:" + result.getResultCode(), Toast.LENGTH_SHORT).show();
//                    if (result.getResultCode() == RESULT_OK) {
//                        // 用户授权，继续安装
//                        File file = new File(activity.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "update.apk");
//                        installApk(file);
//                    } else {
//                        // 用户拒绝授权，提示用户
//                        Toast.makeText(this.activity, "需要授权才能安装应用", Toast.LENGTH_SHORT).show();
//                    }
//                }
                new ActivityResultCallback<ActivityResult>() {
                    @Override
                    public void onActivityResult(ActivityResult result) {
                        Toast.makeText(activity, "getresultCode:" + result.getResultCode(), Toast.LENGTH_SHORT).show();
                        if (result.getResultCode() == Activity.RESULT_OK) {
                            // 用户授权，继续安装
                            File file = new File(activity.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "update.apk");
                            installApk(file);
                        } else {
                            // 用户拒绝授权，提示用户
                            Toast.makeText(activity, "需要授权才能安装应用", Toast.LENGTH_SHORT).show();
                        }
                    }
                }
        );

        // 创建一个线程来执行网络请求
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    URL url = new URL("https://learning-ability-static.ybbywb.com/apk/tsc102.json");
                    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("GET");
                    connection.setConnectTimeout(5000);
                    connection.setReadTimeout(5000);

                    int responseCode = connection.getResponseCode();
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        InputStream inputStream = connection.getInputStream();
                        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                        StringBuilder response = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            response.append(line);
                        }
                        reader.close();
                        inputStream.close();

                        // 解析JSON数据
                        JSONObject jsonObject = new JSONObject(response.toString());
                        int latestVersionCode = jsonObject.getInt("versionCode");
                        String apkUrl = jsonObject.getString("apkUrl");
                        String updateLog = jsonObject.getString("updateLog");

                        // 获取当前应用的版本号
                        PackageInfo packageInfo = activity.getPackageManager().getPackageInfo(activity.getPackageName(), 0);
                        int currentVersionCode = packageInfo.versionCode;

                        // 比较版本号
                        if (latestVersionCode > currentVersionCode) {
                            // 有新版本，提示用户更新
                            activity.runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    showUpdateDialog(apkUrl, updateLog);
                                }
                            });
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }

    public void showUpdateDialog(String apkUrl, String updateLog) {
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("发现新版本");
        builder.setMessage(updateLog);
        builder.setPositiveButton("立即更新", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                downloadApk(apkUrl);
            }
        });
        builder.setNegativeButton("稍后更新", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        builder.setCancelable(false);
        builder.show();
    }

    public void showProgressDialog() {
        this.activity.runOnUiThread(() -> {
            progressDialog = new ProgressDialog(this.activity);
            progressDialog.setTitle("下载更新");
            progressDialog.setMessage("正在下载，请稍候...");
            progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
            progressDialog.setCancelable(false);
            progressDialog.show();
        });
    }

    public void dismissProgressDialog() {
        this.activity.runOnUiThread(() -> {
            if (progressDialog != null && progressDialog.isShowing()) {
                progressDialog.dismiss();
            }
        });
    }

    public void downloadApk(String apkUrl) {
        OkHttpClient client = new OkHttpClient.Builder()
                .addNetworkInterceptor(chain -> {
                    Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                            .body(new ProgressResponseBody(originalResponse.body(), (bytesRead, contentLength, done) -> {
                                // 更新UI需要在主线程中
                                this.activity.runOnUiThread(() -> {
                                    if (progressDialog != null) {
                                        progressDialog.setMax((int) (contentLength / 1024));
                                        progressDialog.setProgress((int) (bytesRead / 1024));
                                    }
                                });
                            }))
                            .build();
                })
                .build();

        Request request = new Request.Builder().url(apkUrl).build();

        // 显示ProgressDialog
        showProgressDialog();

        // 异步请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                e.printStackTrace();
                activity.runOnUiThread(() -> {
                    dismissProgressDialog();
                    Toast.makeText(activity.getApplicationContext(), "下载失败", Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    // 获取APK文件输入流
                    InputStream inputStream = response.body().byteStream();
                    File file = new File(activity.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "update.apk");
                    FileOutputStream fos = new FileOutputStream(file);
                    byte[] buffer = new byte[2048];
                    int len;
                    while ((len = inputStream.read(buffer)) != -1) {
                        fos.write(buffer, 0, len);
                    }
                    fos.close();
                    inputStream.close();

                    // 安装APK
                    installApk(file);
                }
                activity.runOnUiThread(() -> dismissProgressDialog());
            }
        });
    }

    public void installApk(File file) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (!this.activity.getPackageManager().canRequestPackageInstalls()) {
                // 引导用户去设置页面授权
                Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES);
                this.installPermissionLauncher.launch(intent);
                return;
            }
        }

        Uri apkUri = FileProvider.getUriForFile(activity, activity.getPackageName() + ".fileprovider", file);

        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        this.activity.startActivity(intent);
    }
}


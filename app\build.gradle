plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
}

android {
    namespace 'life.w1.pingpang'
    compileSdk 34

    defaultConfig {
        applicationId "life.w1.pingpang"
        minSdk 24
        targetSdk 34
        versionCode 18
        versionName "1.2.5"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation libs.kotlin.stdlib
    compileOnly libs.lombok
    annotationProcessor libs.lombok
    implementation libs.retrofit
    implementation libs.converter.jackson
    implementation libs.jackson.datatype.jsr310
    implementation libs.adapter.rxjava3
    implementation libs.rxjava
    implementation libs.rxandroid
    implementation libs.okhttp
    implementation libs.logging.interceptor
    implementation libs.java.websocket
    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}
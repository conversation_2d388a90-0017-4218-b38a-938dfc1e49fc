package life.w1.pingpang.activity;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.Switch;
import android.widget.TextView;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import life.w1.pingpang.R;

public class ConfigActivity extends AppCompatActivity {

    private static final String TAG = "ConfigActivity";
    private Switch switchHideEvaluation;
    private Switch switchHideTraining;
    private SharedPreferences configPrefs;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_config);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initViews();
        loadSettings();
        setupListeners();
    }

    private void initViews() {
        switchHideEvaluation = findViewById(R.id.switch_hide_evaluation);
        switchHideTraining = findViewById(R.id.switch_hide_training);
        configPrefs = getSharedPreferences("app_config", Context.MODE_PRIVATE);
    }

    private void loadSettings() {
        boolean hideEvaluation = configPrefs.getBoolean("hide_evaluation_modules", false);
        boolean hideTraining = configPrefs.getBoolean("hide_training_modules", true);

        switchHideEvaluation.setChecked(hideEvaluation);
        switchHideTraining.setChecked(hideTraining);
    }

    private void setupListeners() {
        switchHideEvaluation.setOnCheckedChangeListener((buttonView, isChecked) -> {
            configPrefs.edit().putBoolean("hide_evaluation_modules", isChecked).apply();
        });

        switchHideTraining.setOnCheckedChangeListener((buttonView, isChecked) -> {
            configPrefs.edit().putBoolean("hide_training_modules", isChecked).apply();
        });
    }
}

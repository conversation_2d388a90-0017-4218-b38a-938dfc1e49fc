package life.w1.pingpang.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Bundle;
import android.print.PrintAttributes;
import android.print.PrintDocumentAdapter;
import android.print.PrintManager;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;
import android.webkit.PermissionRequest;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import life.w1.pingpang.R;
import life.w1.pingpang.utils.BaseWebAppInterface;


public class ExpressionRealTimeActivity extends AppCompatActivity {

    private static final String TAG = "ExpressionRealTimeActivity";
    private WebView webView;

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "onCreate, savedInstanceState=" + (savedInstanceState != null ? savedInstanceState.toString() : "null"));
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_expression_real_time);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.expressionRealTimeView), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        this.webView = this.findViewById(R.id.expressionRealTimeWebView);
        this.webView.setBackgroundColor(Color.TRANSPARENT);
        this.webView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        this.webView.addJavascriptInterface(new ExpressionRealTimeActivity.JavaScriptInterface(this), "android");
        this.webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                // 显示加载动画
                findViewById(R.id.loading_spinner).setVisibility(View.VISIBLE);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // 隐藏加载动画
                findViewById(R.id.loading_spinner).setVisibility(View.GONE);
            }
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                view.loadUrl(request.getUrl().toString());
                return true;
            }
        });
        this.webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onPermissionRequest(PermissionRequest request) {
                Log.i(TAG, "onPermissionRequest");
                ExpressionRealTimeActivity.this.runOnUiThread(() -> {
                    Log.i(TAG, "onPermissionRequest, runOnUiThread");
                    if (ContextCompat.checkSelfPermission(ExpressionRealTimeActivity.this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
                        Log.i(TAG, "onPermissionRequest, grant");
                        request.grant(request.getResources());
                    } else {
                        Log.i(TAG, "onPermissionRequest, request");
                        ActivityCompat.requestPermissions(ExpressionRealTimeActivity.this, new String[]{Manifest.permission.CAMERA}, 1);
                    }
                });
            }
        });

        WebSettings webSettings = this.webView.getSettings();
        this.webView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        webSettings.setMediaPlaybackRequiresUserGesture(false);
        this.webView.clearCache(true);
//        getWindow().setFlags(
//                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
//                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
//        );
//        webSettings.setDomStorageEnabled(true);
//        webSettings.setAllowUniversalAccessFromFileURLs(true);
//        webSettings.setAllowFileAccessFromFileURLs(true);
//        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        if (ContextCompat.checkSelfPermission(ExpressionRealTimeActivity.this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            this.webView.loadUrl("https://llm.ybbywb.com/emotion_demo");
        } else {
            ActivityCompat.requestPermissions(ExpressionRealTimeActivity.this, new String[]{Manifest.permission.CAMERA}, 1);
        }

//        ImageView closeButton = findViewById(R.id.close_button);
//        closeButton.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                // Close the current activity
//                finish();
//            }
//        });
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy");
        if (webView != null) {
            webView.removeAllViews();
            webView.destroy();
            webView = null;
        }
        super.onDestroy();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 1) {
            if (permissions.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                this.webView.loadUrl("https://llm.ybbywb.com/emotion_demo");
            } else {
                Toast.makeText(this, "No Permission", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private class JavaScriptInterface extends BaseWebAppInterface {

        public JavaScriptInterface(Context context) {
            super(context);
        }

        @JavascriptInterface
        public void onFinish() {
            runOnUiThread(() -> {
                Log.i(TAG, "onFinish");
                ExpressionRealTimeActivity.this.finish();
            });
        }

    }
}
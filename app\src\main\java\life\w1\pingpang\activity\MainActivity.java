package life.w1.pingpang.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;


import org.json.JSONObject;

import java.io.File;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import life.w1.pingpang.R;
import life.w1.pingpang.fragment.CopilotFragment;
import life.w1.pingpang.fragment.ScreeningFragment;
import life.w1.pingpang.fragment.SurveyFragment;
import life.w1.pingpang.fragment.TrainingFragment;
import life.w1.pingpang.fragment.TreatmentFragment;
import life.w1.pingpang.model.dto.ConfigDataDto;
import life.w1.pingpang.model.response.ResponseEntity;
import life.w1.pingpang.service.ApiService;
import life.w1.pingpang.utils.ConfigUtils;
import life.w1.pingpang.utils.LocaleHelper;
import life.w1.pingpang.utils.UpgradeHelper;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";
    private  JSONObject configData;
    private Button btnSelected;

    private Map<Integer, Fragment> fragments;

    @SuppressLint("SetTextI18n")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
//        UpgradeHelper upgradeHelper = new UpgradeHelper();
//        upgradeHelper.checkForUpdate(this, this);
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setConfigData();
        setLanguage();
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        this.btnSelected = findViewById(R.id.btn_main_screening);
        this.btnSelected.setBackgroundColor(ContextCompat.getColor(this, R.color.white));
        this.btnSelected.setTextColor(ContextCompat.getColor(this, R.color.btn_color));
        this.fragments = Map.of(
                R.id.btn_main_screening, new ScreeningFragment(),
                R.id.btn_main_survey, new SurveyFragment(),
                R.id.btn_main_copilot, new CopilotFragment(),
                R.id.btn_main_training, new TrainingFragment()
        );
        this.getSupportFragmentManager().beginTransaction()
                .add(R.id.fragment_container, Objects.requireNonNull(this.fragments.get(R.id.btn_main_screening)))
                .commit();

        TextView screeningNameText = findViewById(R.id.screeningNameText);
        SharedPreferences prefs = getSharedPreferences("user_info", Context.MODE_PRIVATE);
        screeningNameText.setText(prefs.getString("name", "") + " " + getString(R.string.screening));

        TextView tvVersion = findViewById(R.id.tvVersion);
        String versionName = getVersionName(this);
        Integer versionCode = getVersionCode(this);
        ConfigUtils.getEnv(this);
        tvVersion.setText("version " + versionName + "(" + versionCode + ")" + ConfigUtils.getEnv(this));

        TextView tvDevice = findViewById(R.id.tvDevice);
        @SuppressLint("HardwareIds") String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        tvDevice.setText("device " + androidId);

        String is_baichuan = ConfigUtils.isBaiChuan(this);
        if (is_baichuan.equals("1")) {
            Button copilot_btn = findViewById(R.id.btn_main_copilot);
            copilot_btn.setVisibility(View.INVISIBLE);
            Button training_btn = findViewById(R.id.btn_main_training);
            training_btn.setVisibility(View.INVISIBLE);
            Button survey_btn = findViewById(R.id.btn_main_survey);
            survey_btn.setVisibility(View.INVISIBLE);
        }

        // 根据配置隐藏training按钮
        if (ConfigUtils.isHideTrainingModules(this)) {
            Button training_btn = findViewById(R.id.btn_main_training);
            training_btn.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        setLanguage();
    }

    private String getVersionName(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return "Unknown";
        }
    }
    private Integer getVersionCode(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return 0;
        }
    }

    public void setLanguage() {
        String lang = LocaleHelper.getLocale(this);
        LocaleHelper.setLocale(this, lang);
    }

    public void setConfigData() {
        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        Log.d(TAG, "androidId = " + androidId);
        ApiService.getInstance().getConfigData("https://llm.ybbywb.com/api/v1/config/" + androidId)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<ResponseEntity<ConfigDataDto>>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        Log.d("getConfigData", "onSubscribe");
                    }

                    @Override
                    public void onNext(@NonNull ResponseEntity<ConfigDataDto> response) {
                        Log.d("getConfigData", "onNext: " + response);
                        String language = response.getData().getLanguage();
                        String eyemovement_mode = response.getData().getEyemovement_mode();
                        String hrv_duration = response.getData().getHrv_duration();
                        String environment = response.getData().getEnvironment();
                        String is_baichuan = response.getData().getIs_baichuan();
                        int is_hidden_report = response.getData().getIs_hidden_report();
                        SharedPreferences prefs = getSharedPreferences("config_data", Context.MODE_PRIVATE);
                        prefs.edit().putString("language", language).apply();
                        prefs.edit().putString("eyemovement_mode", eyemovement_mode).apply();
                        prefs.edit().putString("hrv_duration", hrv_duration).apply();
                        prefs.edit().putString("environment", environment).apply();
                        prefs.edit().putString("is_baichuan", is_baichuan).apply();
                        prefs.edit().putInt("is_hidden_report", is_hidden_report).apply();
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        Log.d("getConfigData", "onError: " + e);
                        Toast.makeText(MainActivity.this, "Error: " + e, Toast.LENGTH_LONG).show();
                    }

                    @Override
                    public void onComplete() {
                        Log.d("getConfigData", "onComplete");
                    }
                });
    }

    public void showPanel(View v) {
        if (this.btnSelected == v) {
            return;
        }
        if (this.btnSelected != null) {
            this.btnSelected.setBackgroundColor(ContextCompat.getColor(this, R.color.btn_color));
            this.btnSelected.setTextColor(ContextCompat.getColor(this, R.color.white));
        }
        this.btnSelected = (Button) v;
        this.btnSelected.setBackgroundColor(ContextCompat.getColor(this, R.color.white));
        this.btnSelected.setTextColor(ContextCompat.getColor(this, R.color.btn_color));
        int value = v.getId();
        Fragment temp_fragments = this.fragments.get(v.getId());
        this.getSupportFragmentManager().beginTransaction()
                .replace(R.id.fragment_container, Objects.requireNonNull(this.fragments.get(v.getId())))
                .commit();
    }
}
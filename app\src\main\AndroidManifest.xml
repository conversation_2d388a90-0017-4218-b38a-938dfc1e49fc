<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="com.mitdd.gazetracker.permission.GAZE_TRACK" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.BIND_EXTERNAL_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/Theme.Pingpang"
        tools:targetApi="31">

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="life.w1.pingpang.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity
            android:name=".activity.EyeActivity"
            android:exported="false" />
        <activity
            android:name=".activity.HrvActivity"
            android:exported="false" />
        <activity
            android:name=".activity.EmotionActivity"
            android:exported="false" />
        <activity
            android:name=".activity.SpeechActivity"
            android:exported="false" />
        <activity
            android:name=".activity.AgentActivity"
            android:exported="false" />
        <activity
            android:name=".activity.ExpressionRealTimeActivity"
            android:exported="false" />
        <activity
            android:name=".activity.CopilotActivity"
            android:exported="false" />
        <activity
            android:name=".activity.WebActivity"
            android:exported="false" />
        <activity
            android:name=".ScreeningActivity"
            android:exported="false" />
        <activity
            android:name=".activity.MainActivity"
            android:exported="false">
        </activity>
        <activity
            android:name=".activity.ConfigActivity"
            android:exported="false" />
        <activity
            android:name=".activity.LoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>
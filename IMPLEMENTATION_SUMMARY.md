# 图标配置功能实现总结

## 完成的修改

### 1. ConfigUtils.java 扩展
**文件**: `app/src/main/java/life/w1/pingpang/utils/ConfigUtils.java`

**新增功能**:
- `getIconsPerRow(Context)` - 获取每行图标数量配置
- `setIconsPerRow(Context, int)` - 设置每行图标数量配置
- `isIconVisible(Context, String)` - 检查特定图标是否应该显示
- `setIconVisibility(Context, String, boolean)` - 设置特定图标的显示状态
- `isHideHrv(Context)` - 检查是否隐藏HRV功能
- `isHideSpeech(Context)` - 检查是否隐藏语音功能
- `isHideEmotion(Context)` - 检查是否隐藏情绪功能

**支持的图标ID**:
- `btn_screening_hrv` - HRV压力检测
- `btn_screening_eye` - 眼动追踪
- `btn_screening_speech` - 语音分析
- `btn_screening_emotion` - 情绪识别
- `btn_screening_expression_real_time` - 实时表情
- `btn_screening_calibration` - 校准功能

### 2. ScreeningFragment.java 优化
**文件**: `app/src/main/java/life/w1/pingpang/fragment/ScreeningFragment.java`

**修改内容**:
- **filterVisibleButtons()**: 增加了基于新配置系统的图标过滤逻辑
- **createButtonRows()**: 使用配置的每行图标数量替代硬编码的值
- **createButtonLayout()**: 添加了图标可见性检查（虽然在此处检查是冗余的，但保证了一致性）
- **onResume()**: 添加了配置更改后的界面刷新功能

**关键改进**:
1. **性能优化**: 隐藏的图标完全跳过加载过程
2. **动态布局**: 根据配置动态调整每行图标数量
3. **向后兼容**: 保持与现有配置系统的兼容性
4. **实时刷新**: 配置更改后自动刷新界面

### 3. ConfigActivity.java 增强
**文件**: `app/src/main/java/life/w1/pingpang/activity/ConfigActivity.java`

**新增控件**:
- `Spinner spinnerIconsPerRow` - 每行图标数量选择器
- `Switch switchHideHrv` - HRV功能开关
- `Switch switchHideSpeech` - 语音功能开关
- `Switch switchHideEmotion` - 情绪功能开关

**新增方法**:
- `setupIconsPerRowSpinner()` - 设置图标数量选择器
- 扩展了 `loadSettings()` 和 `setupListeners()` 方法

### 4. activity_config.xml 布局更新
**文件**: `app/src/main/res/layout/activity_config.xml`

**新增配置项**:
- 每行图标数量选择器（支持1-5个图标）
- HRV功能显示/隐藏开关
- 语音功能显示/隐藏开关
- 情绪功能显示/隐藏开关

### 5. 测试文件
**文件**: `app/src/test/java/life/w1/pingpang/ConfigUtilsTest.java`

**测试覆盖**:
- 每行图标数量的获取和设置
- 图标可见性的检查和设置
- 各种隐藏功能的状态检查
- 默认值和边界情况测试

## 实现的核心需求

### ✅ 1. 配置控制图标加载
- 隐藏的图标完全跳过加载过程
- 不进行任何网络请求或资源加载
- 通过 `ConfigUtils.isIconVisible()` 实现精确控制

### ✅ 2. 每行布局规则
- 支持1-5个图标每行的配置
- 通过 `ConfigUtils.getIconsPerRow()` 获取配置
- 动态创建行布局，自动处理间距

### ✅ 3. 只加载显示的图标
- 在 `filterVisibleButtons()` 中过滤不可见图标
- 确保只有可见图标才会被创建和加载
- 优化了内存使用和加载性能

### ✅ 4. 配置一致性
- 图标的加载顺序和显示位置与配置保持一致
- 支持实时配置更改和界面刷新
- 保持与现有配置系统的向后兼容

## 配置存储结构

**SharedPreferences文件**: `app_config`

**配置键值**:
- `icons_per_row`: 每行图标数量 (默认: 3)
- `hide_hrv`: 隐藏HRV功能 (默认: false)
- `hide_eye_tracking`: 隐藏眼动功能 (默认: true)
- `hide_speech`: 隐藏语音功能 (默认: false)
- `hide_emotion`: 隐藏情绪功能 (默认: false)
- `hide_calibration`: 隐藏校准功能 (默认: true)
- `hide_real_time_expression`: 隐藏实时表情功能 (默认: true)
- `hide_training_modules`: 隐藏训练模块功能 (默认: true)

## 使用方式

1. **通过配置界面**: 在登录页面连续点击5次进入ConfigActivity
2. **通过代码**: 使用ConfigUtils类的静态方法进行配置
3. **自动生效**: 配置更改后，重新进入ScreeningFragment即可看到效果

## 注意事项

- 配置更改需要重新进入Fragment才能生效
- 建议至少保留一个图标可见
- 每行图标数量建议根据屏幕尺寸合理设置
- 所有配置都持久化存储，应用重启后仍然有效

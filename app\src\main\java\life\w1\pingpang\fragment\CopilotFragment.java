package life.w1.pingpang.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;

import life.w1.pingpang.R;
import life.w1.pingpang.activity.CopilotActivity;
import life.w1.pingpang.activity.EyeActivity;
import life.w1.pingpang.activity.MainActivity;

public class CopilotFragment extends Fragment {

    public CopilotFragment() {

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.fragment_copilot, container, false);
        view.findViewById(R.id.btn_copilot_medical).setOnClickListener(this::showMedicalReportAssistant);
        return view;
    }

    public void showMedicalReportAssistant(View v) {
        Intent intent = new Intent(new Intent(this.getActivity(), CopilotActivity.class));
        startActivity(intent);
    }
}
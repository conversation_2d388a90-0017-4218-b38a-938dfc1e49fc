package life.w1.pingpang.service;

import android.content.Context;

import androidx.annotation.NonNull;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.IOException;

import io.reactivex.rxjava3.core.Observable;
import life.w1.pingpang.model.dto.ConfigDataDto;
import life.w1.pingpang.model.dto.ScreeningDetailDto;
import life.w1.pingpang.model.dto.ScreeningInfoDto;
import life.w1.pingpang.model.dto.ScreeningRegisterDto;
import life.w1.pingpang.model.request.CreateScreeningRequest;
import life.w1.pingpang.model.request.SaveScreeningDetailRequest;
import life.w1.pingpang.model.request.ScreeningRegisterRequest;
import life.w1.pingpang.model.response.ResponseEntity;
import life.w1.pingpang.utils.ConfigUtils;
import lombok.Getter;
import lombok.Setter;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava3.RxJava3CallAdapterFactory;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Url;

public class ApiService {

    @Getter
    private static final ApiService instance = new ApiService();

    private ApiService() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new HttpLoggingInterceptor()
                        .setLevel(HttpLoggingInterceptor.Level.BODY))
                .addInterceptor(new Interceptor() {
                    @NonNull
                    @Override
                    public Response intercept(@NonNull Chain chain) throws IOException {
                        Request request = chain.request().newBuilder()
//                                .header("Authorization", String.format("Bearer %s", ApiService.this.token))
                                .header("TS-Auth-Token", ApiService.this.token)
                                .build();
                        return chain.proceed(request);
                    }
                })
                .build();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl("https://llm.ybbywb.com/")
//                .baseUrl("https://tsc-llm-test.ybbywb.com/")
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .addCallAdapterFactory(RxJava3CallAdapterFactory.create())
                .client(client)
                .build();

        this.api = retrofit.create(Api.class);
    }

    @Getter
    @Setter
    public String token = "";

    private final Api api;

    private interface Api {

        @POST("/api/v1/screening/create_em_screening")
        Observable<ResponseEntity<ScreeningInfoDto>> createScreening(@Body CreateScreeningRequest createScreeningRequest);

        @POST("/api/v1/screening/save_em_screening_detail")
        Observable<ResponseEntity<ScreeningDetailDto>> saveScreeningDetail(@Body SaveScreeningDetailRequest saveScreeningDetailRequest);

        @GET
        Observable<ResponseEntity<ConfigDataDto>> getConfigData(@Url String url);

        @POST("api/v1/screening/register")
        Observable<ResponseEntity<ScreeningRegisterDto>> registerScreening(@Body ScreeningRegisterRequest screeningRegisterRequest);

    }

    public Observable<ResponseEntity<ScreeningInfoDto>> createScreening(CreateScreeningRequest createScreeningRequest) {
        return this.api.createScreening(createScreeningRequest);
    }

    public Observable<ResponseEntity<ScreeningDetailDto>> saveScreeningDetail(SaveScreeningDetailRequest saveScreeningDetailRequest) {
        return this.api.saveScreeningDetail(saveScreeningDetailRequest);
    }

    public Observable<ResponseEntity<ConfigDataDto>> getConfigData(@Url String url) {
        return this.api.getConfigData(url);
    }

    public Observable<ResponseEntity<ScreeningRegisterDto>> registerScreening(ScreeningRegisterRequest screeningRegisterRequest) {
        return this.api.registerScreening(screeningRegisterRequest);
    }
}

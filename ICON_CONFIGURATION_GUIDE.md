# 图标配置功能使用指南

## 概述

本指南介绍了如何使用新的图标配置功能来控制ScreeningFragment中图标的显示和布局。

## 功能特性

### 1. 图标显示/隐藏控制
- 可以单独控制每个图标的显示状态
- 支持的图标包括：HRV、眼动、语音、情绪、实时表情、校准
- 隐藏的图标将完全跳过加载过程，不进行任何网络请求或资源加载

### 2. 每行布局配置
- 可以配置每行显示的图标数量（1-5个）
- 动态调整布局以适应不同的显示需求

## 配置方式

### 通过ConfigActivity配置界面

1. **进入配置页面**：
   - 在登录页面连续点击5次隐藏区域进入配置页面

2. **配置每行图标数量**：
   - 使用"每行图标数量"下拉菜单选择1-5个图标

3. **配置图标显示状态**：
   - 使用各个功能的开关来控制图标的显示/隐藏
   - 开关状态：
     - 关闭 = 显示图标
     - 开启 = 隐藏图标

### 通过代码配置

```java
// 设置每行图标数量
ConfigUtils.setIconsPerRow(context, 3);

// 获取每行图标数量
int iconsPerRow = ConfigUtils.getIconsPerRow(context);

// 设置特定图标的显示状态
ConfigUtils.setIconVisibility(context, "btn_screening_hrv", true); // 显示HRV图标
ConfigUtils.setIconVisibility(context, "btn_screening_eye", false); // 隐藏眼动图标

// 检查特定图标是否可见
boolean isVisible = ConfigUtils.isIconVisible(context, "btn_screening_hrv");
```

## 支持的图标ID

- `btn_screening_hrv` - HRV压力检测
- `btn_screening_eye` - 眼动追踪
- `btn_screening_speech` - 语音分析
- `btn_screening_emotion` - 情绪识别
- `btn_screening_expression_real_time` - 实时表情
- `btn_screening_calibration` - 校准功能

## 配置存储

所有配置都存储在SharedPreferences中：
- 文件名：`app_config`
- 每行图标数量：`icons_per_row`
- 图标隐藏状态：`hide_[功能名]`

## 向后兼容性

新的配置系统保持与现有配置的向后兼容：
- `is_baichuan` 配置仍然有效
- `hide_evaluation_modules` 配置仍然有效
- 新配置与旧配置同时生效，采用更严格的隐藏策略

## 实现细节

### 图标加载优化
- 只有在图标配置为显示时才加载图标资源
- 隐藏的图标完全跳过加载过程，提高性能
- 支持动态刷新，配置更改后立即生效

### 布局自适应
- 根据配置的每行图标数量动态创建行布局
- 自动处理行间距和图标间距
- 支持不同屏幕尺寸的自适应显示

## 注意事项

1. 配置更改后需要重新进入ScreeningFragment才能看到效果
2. 如果所有图标都被隐藏，页面将显示为空白
3. 建议至少保留一个图标可见以保证用户体验
4. 每行图标数量建议根据屏幕尺寸合理设置（平板建议3-4个，手机建议2-3个）

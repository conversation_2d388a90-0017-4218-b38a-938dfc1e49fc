package life.w1.pingpang.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScreeningInfoDto {

    private String id;

    private String profileId;

    private String category;

    private String report;

    private Integer status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

}

package life.w1.pingpang.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.JavascriptInterface;
import android.webkit.PermissionRequest;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import android.provider.Settings;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

import life.w1.pingpang.R;
import life.w1.pingpang.utils.BaseWebAppInterface;
import life.w1.pingpang.utils.ConfigUtils;
import life.w1.pingpang.utils.LocaleHelper;

import android.widget.Button;
import android.print.PrintManager;
import android.print.PrintAttributes;
import android.print.PrintDocumentAdapter;

public class HrvActivity extends AppCompatActivity {

    private static final String TAG = "HrvActivity";
    private String VERSION_URL;
    private String HRV_URL;
//    private String CONFIG_URL;
    private static final String PREFS_NAME = "HrvPrefs";
    private static final String VERSION_KEY = "version";
    private WebView webView;
    private String configData;

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "onCreate, savedInstanceState=" + (savedInstanceState != null ? savedInstanceState.toString() : "null"));
        super.onCreate(savedInstanceState);

        String BASE_URL = ConfigUtils.getBaseUrl(this);
        VERSION_URL = BASE_URL + "/version.json";
        HRV_URL = BASE_URL + "/hrvtest";
//        CONFIG_URL =  BASE_URL + "/api/v1/config/hrv/";

        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_hrv);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.hrv_track), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        //Button printButton = findViewById(R.id.printButton);

        this.webView = this.findViewById(R.id.webview_hrv);
        this.webView.setBackgroundColor(Color.TRANSPARENT);
        this.webView.addJavascriptInterface(new HrvActivity.JavaScriptInterface(this), "android");
        this.webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                // 显示加载动画
                findViewById(R.id.loading_spinner).setVisibility(View.VISIBLE);
//                Handler handler = new Handler(Looper.getMainLooper());
//                Runnable runnable = new Runnable() {
//                    @Override
//                    public void run() {
//                        // 在这里执行延迟后的操作
//                        findViewById(R.id.loading_spinner).setVisibility(View.GONE);
//                    }
//                };
//                handler.postDelayed(runnable, 3000);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // 隐藏加载动画
                findViewById(R.id.loading_spinner).setVisibility(View.GONE);
            }
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                view.loadUrl(request.getUrl().toString());
                return true;
            }

            @Nullable
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                String url = request.getUrl().toString();

                // 只拦截特定资源，其他资源走网络
                if (shouldInterceptLocalResource(url)) {
                    try {
                        String fileName = getFileNameFromUrl(url);
                        InputStream is = getAssets().open("web/" + fileName);
                        WebResourceResponse return_info = new WebResourceResponse(getMimeType(fileName), "UTF-8", is);
                        return return_info;
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

                // 不需要拦截的资源返回null，让WebView自己处理
                return null;
            }

        });
        this.webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onPermissionRequest(PermissionRequest request) {
                Log.i(TAG, "onPermissionRequest");
                request.grant(request.getResources());

                /*
                HrvActivity.this.runOnUiThread(() -> {
                    Log.i(TAG, "onPermissionRequest, runOnUiThread");
                    if (ContextCompat.checkSelfPermission(HrvActivity.this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
                        Log.i(TAG, "onPermissionRequest, grant");
                        request.grant(request.getResources());
                    } else {
                        Log.i(TAG, "onPermissionRequest, request");
                        ActivityCompat.requestPermissions(HrvActivity.this, new String[]{Manifest.permission.CAMERA}, 1);
                    }
                });
                */
            }

            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                // 使用Logcat输出日志
                Log.d("WebViewConsole", consoleMessage.message() + " -- From line "
                        + consoleMessage.lineNumber() + " of " + consoleMessage.sourceId());
                return super.onConsoleMessage(consoleMessage);
            }
        });

        WebSettings webSettings = this.webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setMediaPlaybackRequiresUserGesture(false);
        webSettings.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
        
        // 启用混合内容（HTTP和HTTPS混合）
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        this.webView.clearCache(true);
//        WebView.setWebContentsDebuggingEnabled(true);

        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);

        checkVersionAndUpdateCache(() -> {
            if (ContextCompat.checkSelfPermission(HrvActivity.this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "loading " + HRV_URL + "?config=" + this.configData + "&token=" + ConfigUtils.getUserToken(this));
                this.webView.loadUrl(HRV_URL + "?config=" + this.configData + "&device_id=" + androidId + "&lang=" + ConfigUtils.getLang(this) + "&token=" + ConfigUtils.getUserToken(this));
            } else {
                ActivityCompat.requestPermissions(HrvActivity.this, new String[]{Manifest.permission.CAMERA}, 1);
            }
        });

        //printButton.setOnClickListener(v -> createWebPrintJob(webView));
    }

    private void createWebPrintJob(WebView webView) {
        PrintManager printManager = (PrintManager) getSystemService(PRINT_SERVICE);
        String jobName = getString(R.string.app_name) + " Document";

        PrintDocumentAdapter printAdapter = webView.createPrintDocumentAdapter(jobName);

        printManager.print(jobName, printAdapter,
                new PrintAttributes.Builder()
                        .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                        .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                        .setResolution(new PrintAttributes.Resolution("pdf", "pdf", 600, 600))
                        .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                        .build());
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy");
        if (webView != null) {
            webView.removeAllViews();
            webView.destroy();
            webView = null;
        }
        super.onDestroy();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        if (requestCode == 1) {
            if (permissions.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "loading " + HRV_URL + "?config=" + this.configData + "&device_id=" + androidId);
                this.webView.loadUrl(HRV_URL + "?config=" + this.configData + "&device_id=" + androidId + "&lang=" + ConfigUtils.getLang(this) + "&token=" + ConfigUtils.getUserToken(this));
            } else {
                Toast.makeText(this, "No Permission", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private boolean shouldInterceptLocalResource(String url) {
        // 定义需要本地加载的资源类型和路径
        List<String> localPaths = Arrays.asList(
                "/opencv",
                "/mediapipe"
        );

        for (String path : localPaths) {
            if (url.contains(path)) {
                return true;
            }
        }
        return false;
    }

    private String getFileNameFromUrl(String url) {
        try {
            // 解析URL
            URL urlObj = new URL(url);
            // 获取路径部分
            String path = urlObj.getPath();

            // 移除开头的斜杠(如果存在)
            if (path.startsWith("/")) {
                path = path.substring(1);
            }

            return path;
        } catch (MalformedURLException e) {
            // URL解析失败的情况下，使用简单的字符串处理
            String[] parts = url.split("//");
            if (parts.length > 1) {
                // 取host之后的部分
                int index = parts[1].indexOf("/");
                if (index != -1) {
                    return parts[1].substring(index + 1);
                }
            }
            return url;
        }
    }

    private String getMimeType(String fileName) {
        // 根据文件扩展名返回MIME类型
        if (fileName.endsWith(".js")) {
            return "application/javascript";
        } else if (fileName.endsWith(".css")) {
            return "text/css";
        } else if (fileName.endsWith(".png")) {
            return "image/png";
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".woff2")) {
            return "font/woff2";
        }
        return "text/plain";
    }

    private class JavaScriptInterface extends BaseWebAppInterface {

        public JavaScriptInterface(Context context) {
            super(context);
        }

        @JavascriptInterface
        public void onFinish() {
            runOnUiThread(() -> {
                Log.i(TAG, "onFinish");
                HrvActivity.this.finish();  // 在主线程中执行 finish
            });
        }

        @JavascriptInterface
        public void simulateClick() {
            runOnUiThread(() -> {
                // 模拟点击 VideoWrapper 的逻辑
                View videoWrapper = findViewById(R.id.webview_hrv);
                if (videoWrapper != null) {
                    videoWrapper.performClick();  // 模拟点击
                    Log.i(TAG, "simulateClick: VideoWrapper clicked");
                    videoWrapper.post(() -> videoWrapper.invalidate());
                }
            });
        }

        @JavascriptInterface
        public void printPage() {
            ((HrvActivity)context).runOnUiThread(() -> {
                WebView webView = ((HrvActivity)context).findViewById(R.id.webview_hrv);
                ((HrvActivity)context).createWebPrintJob(webView);
            });
        }

        @JavascriptInterface
        public void saveVideo(String recordId, String base64String) {
            byte[] data = Base64.decode(base64String, Base64.DEFAULT);
            saveByteArrayToFile(data, recordId + "_video.webm");
        }
    }

    private void saveByteArrayToFile(byte[] data, String filename) {
        try {
            File path = getExternalFilesDir(null);
            File file = new File(path, filename);
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(data);
            fos.close();
//            // 提示保存成功
//            runOnUiThread(() -> {
//                Toast.makeText(HrvActivity.this, "视频保存成功: " + file.getAbsolutePath(), Toast.LENGTH_LONG).show();
//            });
        } catch (IOException e) {
            e.printStackTrace();
//            // 提示保存失败
//            runOnUiThread(() -> {
//                Toast.makeText(HrvActivity.this, "视频保存失败", Toast.LENGTH_LONG).show();
//            });
        }
    }

    public interface VersionCheckCallback {
        void onVersionCheckCompleted();
    }

    private void checkVersionAndUpdateCache(VersionCheckCallback callback) {
        new Thread(() -> {
            try {
                // 从 URL 获取 JSON 数据
                URL url = new URL(VERSION_URL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                // 解析 JSON 数据
                JSONObject jsonObject = new JSONObject(response.toString());
                String currentVersion = jsonObject.getString("version");
                Log.d(TAG, "Current version from server: " + currentVersion);

                // 获取本地存储的版本
                SharedPreferences prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
                String savedVersion = prefs.getString(VERSION_KEY, null);
                Log.d(TAG, "Saved version: " + savedVersion);

                // 如果版本不同，清除缓存并保存新的版本
                if (savedVersion == null || !savedVersion.equals(currentVersion)) {
                    runOnUiThread(() -> webView.clearCache(true));
                    prefs.edit().putString(VERSION_KEY, currentVersion).apply();
                    Log.d(TAG, "Cache cleared and new version saved: " + currentVersion);
                } else {
                    Log.d(TAG, "Version is up to date, no action needed.");
                }

//                String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
//                Log.d(TAG, "androidId = " + androidId);

//                URL configUrl = new URL(CONFIG_URL + androidId);
//                HttpURLConnection configConnection = (HttpURLConnection) configUrl.openConnection();
//                configConnection.setRequestMethod("GET");
//                configConnection.setConnectTimeout(5000);
//                configConnection.setReadTimeout(5000);
//                BufferedReader configReader = new BufferedReader(new InputStreamReader(configConnection.getInputStream()));
//                StringBuilder configResponse = new StringBuilder();
//                while ((line = configReader.readLine()) != null) {
//                    configResponse.append(line);
//                }
//                configReader.close();
//
//                // 解析配置数据
//                JSONObject configJson = new JSONObject(configResponse.toString());
//                JSONObject dataJson = configJson.getJSONObject("data");
//                String configStr = dataJson.toString();  // 将配置数据转化为 JSON 字符串，或者处理为你需要的结构
//                Log.d(TAG, "Config data from server: " + configStr);
//
//                try {
//                    this.configData = URLEncoder.encode(configStr, "UTF-8");
//                } catch (UnsupportedEncodingException e) {
//                    throw new RuntimeException(e);
//                }

                SharedPreferences hrv_prefs = getSharedPreferences("config_data", Context.MODE_PRIVATE);
                String hrv_duration = hrv_prefs.getString("hrv_duration", "90");
                String configStr = String.format("{\"duration\":%s}", hrv_duration);
                this.configData = URLEncoder.encode(configStr, "UTF-8");

                // 通知回调版本检查完成
                runOnUiThread(callback::onVersionCheckCompleted);
            } catch (Exception e) {
                Log.e(TAG, "Error checking version", e);
                // 出现错误时，仍然通知回调
                runOnUiThread(callback::onVersionCheckCompleted);
            }
        }).start();
    }
}
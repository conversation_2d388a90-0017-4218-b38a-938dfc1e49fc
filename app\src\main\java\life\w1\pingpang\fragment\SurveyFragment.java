package life.w1.pingpang.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;

import life.w1.pingpang.R;
import life.w1.pingpang.activity.WebActivity;

public class SurveyFragment extends Fragment {

    public SurveyFragment() {

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_survey, container, false);
        view.findViewById(R.id.btn_survey_1).setOnClickListener(this::showSurvey);
        view.findViewById(R.id.btn_survey_2).setOnClickListener(this::showSurvey);
        view.findViewById(R.id.btn_survey_3).setOnClickListener(this::showSurvey);
        view.findViewById(R.id.btn_survey_4).setOnClickListener(this::showSurvey);
        view.findViewById(R.id.btn_survey_5).setOnClickListener(this::showSurvey);
        view.findViewById(R.id.btn_survey_6).setOnClickListener(this::showSurvey);
        view.findViewById(R.id.btn_survey_7).setOnClickListener(this::showSurvey);
        return view;
    }

    public void showSurvey(View v) {
        String tag = v.getTag().toString();
        String url = String.format("https://llm.ybbywb.com/survey/test%s.html", tag);
        Intent intent = new Intent(this.getActivity(), WebActivity.class);
        intent.putExtra("url", url);
        this.requireActivity().startActivity(intent);
    }

}
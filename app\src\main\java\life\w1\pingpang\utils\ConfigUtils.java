package life.w1.pingpang.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import life.w1.pingpang.R;

public class ConfigUtils {

    public static String getBaseUrl(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("config_data", Context.MODE_PRIVATE);
        String environment = prefs.getString("environment", "prod");
        if (environment.equals("test")) {
            return context.getString(R.string.base_url_test);
        }
        return context.getString(R.string.base_url);
    }

    public static String getEnv(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("config_data", Context.MODE_PRIVATE);
        String environment = prefs.getString("environment", "prod");
        if (environment.equals("test")) {
            return environment;
        }
        return "";
    }

    public static String getLang(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("config_data", Context.MODE_PRIVATE);
        return prefs.getString("language", "zh");

    }

    public static String isBaiChuan(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("config_data", Context.MODE_PRIVATE);
        return prefs.getString("is_baichuan", "0");
    }

    public static Integer isHiddenReport(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("config_data", Context.MODE_PRIVATE);
        return prefs.getInt("is_hidden_report", 0);
    }

    public static String getUserToken(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("user_info", Context.MODE_PRIVATE);
        return prefs.getString("token", "");
    }

    public static Integer getVersionCode(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return 0;
        }
    }

    public static boolean isHideEvaluationModules(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        return prefs.getBoolean("hide_evaluation_modules", true); // 默认隐藏
    }

    public static boolean isHideEyeTracking(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        return prefs.getBoolean("hide_eye_tracking", true); // 默认隐藏
    }

    public static boolean isHideCalibration(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        return prefs.getBoolean("hide_calibration", true); // 默认隐藏
    }

    public static boolean isHideRealTimeExpression(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        return prefs.getBoolean("hide_real_time_expression", true); // 默认隐藏
    }

    public static boolean isHideTrainingModules(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        return prefs.getBoolean("hide_training_modules", true); // 默认隐藏
    }

    // 获取每行图标数量配置
    public static int getIconsPerRow(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        return prefs.getInt("icons_per_row", 3); // 默认每行3个
    }

    // 设置每行图标数量配置
    public static void setIconsPerRow(Context context, int iconsPerRow) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        prefs.edit().putInt("icons_per_row", iconsPerRow).apply();
    }

    // 检查特定图标是否应该显示
    public static boolean isIconVisible(Context context, String iconId) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);

        // 根据具体的图标ID检查配置
        switch (iconId) {
            case "btn_screening_hrv":
                return !prefs.getBoolean("hide_hrv", false); // 默认显示
            case "btn_screening_eye":
                return !isHideEyeTracking(context);
            case "btn_screening_speech":
                return !prefs.getBoolean("hide_speech", false); // 默认显示
            case "btn_screening_emotion":
                return !prefs.getBoolean("hide_emotion", false); // 默认显示
            case "btn_screening_expression_real_time":
                return !isHideRealTimeExpression(context);
            case "btn_screening_calibration":
                return !isHideCalibration(context);
            default:
                return true; // 默认显示未知图标
        }
    }

    // 设置特定图标的显示状态
    public static void setIconVisibility(Context context, String iconId, boolean isVisible) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);

        switch (iconId) {
            case "btn_screening_hrv":
                prefs.edit().putBoolean("hide_hrv", !isVisible).apply();
                break;
            case "btn_screening_eye":
                prefs.edit().putBoolean("hide_eye_tracking", !isVisible).apply();
                break;
            case "btn_screening_speech":
                prefs.edit().putBoolean("hide_speech", !isVisible).apply();
                break;
            case "btn_screening_emotion":
                prefs.edit().putBoolean("hide_emotion", !isVisible).apply();
                break;
            case "btn_screening_expression_real_time":
                prefs.edit().putBoolean("hide_real_time_expression", !isVisible).apply();
                break;
            case "btn_screening_calibration":
                prefs.edit().putBoolean("hide_calibration", !isVisible).apply();
                break;
        }
    }

    // 检查是否隐藏HRV功能
    public static boolean isHideHrv(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        return prefs.getBoolean("hide_hrv", false); // 默认显示
    }

    // 检查是否隐藏语音功能
    public static boolean isHideSpeech(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        return prefs.getBoolean("hide_speech", false); // 默认显示
    }

    // 检查是否隐藏情绪功能
    public static boolean isHideEmotion(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);
        return prefs.getBoolean("hide_emotion", false); // 默认显示
    }
}

package life.w1.pingpang.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;


import org.json.JSONObject;

import java.io.File;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import life.w1.pingpang.R;
import life.w1.pingpang.fragment.CopilotFragment;
import life.w1.pingpang.fragment.ScreeningFragment;
import life.w1.pingpang.fragment.SurveyFragment;
import life.w1.pingpang.fragment.TrainingFragment;
import life.w1.pingpang.fragment.TreatmentFragment;
import life.w1.pingpang.model.dto.ConfigDataDto;
import life.w1.pingpang.model.dto.ScreeningInfoDto;
import life.w1.pingpang.model.dto.ScreeningRegisterDto;
import life.w1.pingpang.model.request.CreateScreeningRequest;
import life.w1.pingpang.model.request.ScreeningRegisterRequest;
import life.w1.pingpang.model.response.ResponseEntity;
import life.w1.pingpang.service.ApiService;
import life.w1.pingpang.utils.ConfigUtils;
import life.w1.pingpang.utils.LocaleHelper;
import life.w1.pingpang.utils.MultiClickListener;
import life.w1.pingpang.utils.UpgradeHelper;

public class LoginActivity extends AppCompatActivity {

    private static final String TAG = "LoginActivity";
    private  JSONObject configData;
    private Button btnSelected;
    private ScreeningRegisterDto screeningTokenInfo;
    private Spinner spinnerAge, spinnerGrade, spinnerClass;
    private String spinnerAgeString, spinnerGradeString, spinnerClassString;

    private Map<Integer, Fragment> fragments;

    @SuppressLint("SetTextI18n")
    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setConfigData();
        setLanguage();
        setContentView(R.layout.activity_login);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        TextView tvVersion = findViewById(R.id.tvVersion);
        String versionName = getVersionName(this);
        Integer versionCode = getVersionCode(this);
        ConfigUtils.getEnv(this);
        tvVersion.setText("version " + versionName + "(" + versionCode + ")" + ConfigUtils.getEnv(this));

        TextView tvDevice = findViewById(R.id.tvDevice);
        @SuppressLint("HardwareIds") String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        tvDevice.setText("device " + androidId);

        // 设置隐藏配置按钮的多次点击监听器
        View viewConfig = findViewById(R.id.view_config);
        viewConfig.setOnClickListener(new MultiClickListener(5, 2000) {
            @Override
            public void onClickValid(View v) {
                // 连点五下后进入配置页面
                Intent intent = new Intent(LoginActivity.this, ConfigActivity.class);
                startActivity(intent);
            }
        });

        // 初始化Spinner
        spinnerAge = findViewById(R.id.spinnerAge);
        spinnerGrade = findViewById(R.id.spinnerGrade);
        spinnerClass = findViewById(R.id.spinnerClass);

        // 设置年龄适配器
        ArrayAdapter<CharSequence> adapterAge = ArrayAdapter.createFromResource(this,
                R.array.age_array, android.R.layout.simple_spinner_item);
        adapterAge.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerAge.setAdapter(adapterAge);

        // 设置年级适配器
        ArrayAdapter<CharSequence> adapterGrade = ArrayAdapter.createFromResource(this,
                R.array.grade_array, android.R.layout.simple_spinner_item);
        adapterGrade.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerGrade.setAdapter(adapterGrade);

        // 设置班级适配器
        ArrayAdapter<CharSequence> adapterClass = ArrayAdapter.createFromResource(this,
                R.array.class_array, android.R.layout.simple_spinner_item);
        adapterClass.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerClass.setAdapter(adapterClass);

        // 设置监听器
        spinnerAge.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                String selectedAge = parent.getItemAtPosition(position).toString();
                // 处理选择事件
                if (position == 0) {
                    LoginActivity.this.spinnerAgeString = "0";
                } else {
                    LoginActivity.this.spinnerAgeString = selectedAge;
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // 处理未选择事件
            }
        });
        spinnerGrade.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                String selectedGrade = parent.getItemAtPosition(position).toString();
                // 处理选择事件
                if (position == 0) {
                    LoginActivity.this.spinnerGradeString = "";
                } else {
                    LoginActivity.this.spinnerGradeString = selectedGrade;
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // 处理未选择事件
            }
        });
        spinnerClass.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                String selectedClass = parent.getItemAtPosition(position).toString();
                // 处理选择事件
                if (position == 0) {
                    LoginActivity.this.spinnerClassString = "";
                } else {
                    LoginActivity.this.spinnerClassString = selectedClass;
                }

            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // 处理未选择事件
            }
        });

        String is_baichuan = ConfigUtils.isBaiChuan(this);
        if (is_baichuan.equals("1")) {
            spinnerGrade.setVisibility(View.VISIBLE);
            spinnerClass.setVisibility(View.VISIBLE);
        } else {
            spinnerGrade.setVisibility(View.INVISIBLE);
            spinnerClass.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        setLanguage();
    }

    private String getVersionName(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return "Unknown";
        }
    }
    private Integer getVersionCode(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return 0;
        }
    }

    public void setLanguage() {
        String lang = LocaleHelper.getLocale(this);
        LocaleHelper.setLocale(this, lang);
    }

    public void setConfigData() {
        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        Log.d(TAG, "androidId = " + androidId);
        ApiService.getInstance().getConfigData("https://llm.ybbywb.com/api/v1/config/" + androidId)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<ResponseEntity<ConfigDataDto>>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        Log.d("getConfigData", "onSubscribe");
                    }

                    @Override
                    public void onNext(@NonNull ResponseEntity<ConfigDataDto> response) {
                        Log.d("getConfigData", "onNext: " + response);
                        String language = response.getData().getLanguage();
                        String eyemovement_mode = response.getData().getEyemovement_mode();
                        String hrv_duration = response.getData().getHrv_duration();
                        String environment = response.getData().getEnvironment();
                        String is_baichuan = response.getData().getIs_baichuan();
                        int is_hidden_report = response.getData().getIs_hidden_report();
                        SharedPreferences prefs = getSharedPreferences("config_data", Context.MODE_PRIVATE);
                        prefs.edit().putString("language", language).apply();
                        prefs.edit().putString("eyemovement_mode", eyemovement_mode).apply();
                        prefs.edit().putString("hrv_duration", hrv_duration).apply();
                        prefs.edit().putString("environment", environment).apply();
                        prefs.edit().putString("is_baichuan", is_baichuan).apply();
                        prefs.edit().putInt("is_hidden_report", is_hidden_report).apply();
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        Log.d("getConfigData", "onError: " + e);
                        Toast.makeText(LoginActivity.this, "Error: " + e, Toast.LENGTH_LONG).show();
                    }

                    @Override
                    public void onComplete() {
                        Log.d("getConfigData", "onComplete");
                    }
                });
    }

    public void startScreening() {
        SharedPreferences prefs = getSharedPreferences("user_info", Context.MODE_PRIVATE);
        prefs.edit().putString("token", this.screeningTokenInfo.getToken()).apply();
        prefs.edit().putString("name", this.screeningTokenInfo.getName()).apply();
        Intent intent = new Intent(LoginActivity.this, MainActivity.class);
        startActivity(intent);
    }

    public void showPanel(View v) {
        if (this.btnSelected == v) {
            return;
        }
        if (this.btnSelected != null) {
            this.btnSelected.setBackgroundColor(ContextCompat.getColor(this, R.color.btn_color));
            this.btnSelected.setTextColor(ContextCompat.getColor(this, R.color.white));
        }
        this.btnSelected = (Button) v;
        this.btnSelected.setBackgroundColor(ContextCompat.getColor(this, R.color.white));
        this.btnSelected.setTextColor(ContextCompat.getColor(this, R.color.btn_color));

        EditText nameEditText = findViewById(R.id.nameEditText);
        RadioGroup genderRadioGroup = findViewById(R.id.genderRadioGroup);
        EditText idnumEditText = findViewById(R.id.idnumEditText);

        int genderId = genderRadioGroup.getCheckedRadioButtonId();
        Integer gender = (genderId == R.id.maleRadioButton) ? 1 : 2;
        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);

        ApiService.getInstance().registerScreening(ScreeningRegisterRequest.builder()
                        .name(nameEditText.getText().toString())
                        .gender(gender)
                        .age(this.spinnerAgeString)
                        .idnum(idnumEditText.getText().toString())
                        .grade(this.spinnerGradeString)
                        .classes(this.spinnerClassString)
                        .device_id(androidId)
                        .build())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<ResponseEntity<ScreeningRegisterDto>>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        Log.d("registerScreening", "onSubscribe");
                    }

                    @Override
                    public void onNext(@NonNull ResponseEntity<ScreeningRegisterDto> response) {
                        Log.d("registerScreening", "onNext: " + response);
                        Integer code = response.getCode();
                        String message = response.getMessage();
                        if (code == 0) {
                            LoginActivity.this.screeningTokenInfo = response.getData();
                            LoginActivity.this.startScreening();
                        } else {
                            Toast.makeText(LoginActivity.this, "ErrorCode: " + code + " Message: " + message, Toast.LENGTH_LONG).show();
                        }
                        LoginActivity.this.btnSelected.setBackgroundColor(ContextCompat.getColor(LoginActivity.this, R.color.btn_color));
                        LoginActivity.this.btnSelected.setTextColor(ContextCompat.getColor(LoginActivity.this, R.color.white));
                        LoginActivity.this.btnSelected = null;
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        Log.d("registerScreening", "onError: " + e);
                        Toast.makeText(LoginActivity.this, "Error: " + e, Toast.LENGTH_LONG).show();
                        LoginActivity.this.btnSelected.setBackgroundColor(ContextCompat.getColor(LoginActivity.this, R.color.btn_color));
                        LoginActivity.this.btnSelected.setTextColor(ContextCompat.getColor(LoginActivity.this, R.color.white));
                        LoginActivity.this.btnSelected = null;
                    }

                    @Override
                    public void onComplete() {
                        Log.d("registerScreening", "onComplete");
                    }
                });

    }
}
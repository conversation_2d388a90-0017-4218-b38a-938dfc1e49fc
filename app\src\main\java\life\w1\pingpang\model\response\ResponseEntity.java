package life.w1.pingpang.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponseEntity<T> {

    @Builder.Default
    private Integer code = 0;

    @Builder.Default
    private String desc = "success";

    @Builder.Default
    private String message = "success";

    @Builder.Default
    private T data = null;

}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".fragment.TrainingFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:context=".fragment.TrainingFragment">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:baselineAligned="false"
            android:orientation="horizontal"
            tools:ignore="UselessParent">

            <LinearLayout
                android:id="@+id/btn_training_eye"
                android:layout_width="128dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center">

                <ImageView
                    android:layout_width="108dp"
                    android:layout_height="108dp"
                    android:contentDescription="@string/btn_training_eye_text"
                    android:scaleType="fitCenter"
                    android:src="@drawable/training" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/btn_training_eye_text"
                    android:textSize="20sp" />
            </LinearLayout>

            <Space
                android:layout_width="32dp"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:layout_width="128dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:padding="16dp">

            </LinearLayout>

            <Space
                android:layout_width="32dp"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:layout_width="128dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:padding="16dp">

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
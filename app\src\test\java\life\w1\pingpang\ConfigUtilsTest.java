package life.w1.pingpang;

import android.content.Context;
import android.content.SharedPreferences;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;

import life.w1.pingpang.utils.ConfigUtils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(RobolectricTestRunner.class)
public class ConfigUtilsTest {

    @Mock
    private Context mockContext;
    
    @Mock
    private SharedPreferences mockPrefs;
    
    @Mock
    private SharedPreferences.Editor mockEditor;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockContext.getSharedPreferences("app_config", Context.MODE_PRIVATE))
                .thenReturn(mockPrefs);
        when(mockPrefs.edit()).thenReturn(mockEditor);
        when(mockEditor.putInt(anyString(), anyInt())).thenReturn(mockEditor);
        when(mockEditor.putBoolean(anyString(), anyBoolean())).thenReturn(mockEditor);
    }

    @Test
    public void testGetIconsPerRow_DefaultValue() {
        // 测试默认值
        when(mockPrefs.getInt("icons_per_row", 3)).thenReturn(3);
        
        int result = ConfigUtils.getIconsPerRow(mockContext);
        
        assertEquals(3, result);
        verify(mockPrefs).getInt("icons_per_row", 3);
    }

    @Test
    public void testSetIconsPerRow() {
        // 测试设置每行图标数量
        ConfigUtils.setIconsPerRow(mockContext, 4);
        
        verify(mockEditor).putInt("icons_per_row", 4);
        verify(mockEditor).apply();
    }

    @Test
    public void testIsIconVisible_HrvIcon() {
        // 测试HRV图标可见性
        when(mockPrefs.getBoolean("hide_hrv", false)).thenReturn(false);
        
        boolean result = ConfigUtils.isIconVisible(mockContext, "btn_screening_hrv");
        
        assertTrue(result);
        verify(mockPrefs).getBoolean("hide_hrv", false);
    }

    @Test
    public void testIsIconVisible_HiddenHrvIcon() {
        // 测试隐藏的HRV图标
        when(mockPrefs.getBoolean("hide_hrv", false)).thenReturn(true);
        
        boolean result = ConfigUtils.isIconVisible(mockContext, "btn_screening_hrv");
        
        assertFalse(result);
    }

    @Test
    public void testSetIconVisibility_ShowHrvIcon() {
        // 测试显示HRV图标
        ConfigUtils.setIconVisibility(mockContext, "btn_screening_hrv", true);
        
        verify(mockEditor).putBoolean("hide_hrv", false);
        verify(mockEditor).apply();
    }

    @Test
    public void testSetIconVisibility_HideHrvIcon() {
        // 测试隐藏HRV图标
        ConfigUtils.setIconVisibility(mockContext, "btn_screening_hrv", false);
        
        verify(mockEditor).putBoolean("hide_hrv", true);
        verify(mockEditor).apply();
    }

    @Test
    public void testIsIconVisible_UnknownIcon() {
        // 测试未知图标（应该默认显示）
        boolean result = ConfigUtils.isIconVisible(mockContext, "unknown_icon");
        
        assertTrue(result);
    }

    @Test
    public void testIsHideHrv_DefaultValue() {
        // 测试HRV隐藏状态默认值
        when(mockPrefs.getBoolean("hide_hrv", false)).thenReturn(false);
        
        boolean result = ConfigUtils.isHideHrv(mockContext);
        
        assertFalse(result);
        verify(mockPrefs).getBoolean("hide_hrv", false);
    }

    @Test
    public void testIsHideSpeech_DefaultValue() {
        // 测试语音隐藏状态默认值
        when(mockPrefs.getBoolean("hide_speech", false)).thenReturn(false);
        
        boolean result = ConfigUtils.isHideSpeech(mockContext);
        
        assertFalse(result);
        verify(mockPrefs).getBoolean("hide_speech", false);
    }

    @Test
    public void testIsHideEmotion_DefaultValue() {
        // 测试情绪隐藏状态默认值
        when(mockPrefs.getBoolean("hide_emotion", false)).thenReturn(false);
        
        boolean result = ConfigUtils.isHideEmotion(mockContext);
        
        assertFalse(result);
        verify(mockPrefs).getBoolean("hide_emotion", false);
    }
}

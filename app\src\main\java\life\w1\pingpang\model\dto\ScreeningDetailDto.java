package life.w1.pingpang.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScreeningDetailDto {

    private String id;

    private String screeningId;

    private String category;

    private String name;

    private String version;

    private String detail;

    private Integer status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

}

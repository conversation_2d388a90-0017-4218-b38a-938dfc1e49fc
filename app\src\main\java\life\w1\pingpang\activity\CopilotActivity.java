package life.w1.pingpang.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.JavascriptInterface;
import android.webkit.PermissionRequest;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import life.w1.pingpang.R;
import life.w1.pingpang.utils.BaseWebAppInterface;
import life.w1.pingpang.utils.ConfigUtils;


public class CopilotActivity extends AppCompatActivity {

    private static final String TAG = "CopilotActivity";
    private WebView webView;
    private static final int REQUEST_AUDIO_PERMISSION_CODE = 1;
    private String BASE_URL;

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "onCreate, savedInstanceState=" + (savedInstanceState != null ? savedInstanceState.toString() : "null"));
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_copilot);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.copilotView), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        this.webView = this.findViewById(R.id.copilotWebView);
        this.webView.setBackgroundColor(Color.TRANSPARENT);
        this.webView.addJavascriptInterface(new CopilotActivity.JavaScriptInterface(this), "android");
        this.webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                // 显示加载动画
                findViewById(R.id.loading_spinner).setVisibility(View.VISIBLE);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // 隐藏加载动画
                findViewById(R.id.loading_spinner).setVisibility(View.GONE);
            }
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                view.loadUrl(request.getUrl().toString());
                return true;
            }
        });
        this.webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onPermissionRequest(final PermissionRequest request) {
                runOnUiThread(() -> {
                    if (ContextCompat.checkSelfPermission(CopilotActivity.this, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
                        // 请求录音权限
                        ActivityCompat.requestPermissions(CopilotActivity.this, new String[]{Manifest.permission.RECORD_AUDIO}, REQUEST_AUDIO_PERMISSION_CODE);
                    } else {
                        // 授予权限
                        request.grant(request.getResources());
                    }
                });
            }

            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                // 使用Logcat输出日志
                Log.d("WebViewConsole", consoleMessage.message() + " -- From line "
                        + consoleMessage.lineNumber() + " of " + consoleMessage.sourceId());
                return super.onConsoleMessage(consoleMessage);
            }
        });

        WebSettings webSettings = this.webView.getSettings();
        webSettings.setDomStorageEnabled(true);
        webSettings.setJavaScriptEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setMediaPlaybackRequiresUserGesture(false);
        webSettings.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
        // this.webView.clearCache(true);

        this.BASE_URL = ConfigUtils.getBaseUrl(this);

        if (ContextCompat.checkSelfPermission(CopilotActivity.this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {

            String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
            String urlPlus = "device_id=" + androidId + "&lang=" + ConfigUtils.getLang(this);
//            this.webView.loadUrl(this.BASE_URL + "/mrassistant?channel_no=huilongguan_hospital&" + urlPlus);
//            this.webView.loadUrl(this.BASE_URL + "/mrassistant?copilot_type=copilot_ikang&" + urlPlus);
            this.webView.loadUrl(this.BASE_URL + "/mrassistant?" + urlPlus);
        } else {
            ActivityCompat.requestPermissions(CopilotActivity.this, new String[]{Manifest.permission.CAMERA}, 1);
        }
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy");
        if (webView != null) {
            webView.removeAllViews();
            webView.destroy();
            webView = null;
        }
        super.onDestroy();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 1) {
            if (permissions.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                this.webView.loadUrl(this.BASE_URL + "/mrassistant");
            } else {
                Toast.makeText(this, "No Permission", Toast.LENGTH_SHORT).show();
            }
        }
    }


    private class JavaScriptInterface extends BaseWebAppInterface {

        protected JavaScriptInterface(Context context) {
            super(context);
        }

        @JavascriptInterface
        public void onFinish() {
            runOnUiThread(() -> {
                Log.i(TAG, "onFinish");
                CopilotActivity.this.finish();
            });
        }

        @JavascriptInterface
        public void simulateClick() {
            runOnUiThread(() -> {
                // 模拟点击 VideoWrapper 的逻辑
                View videoWrapper = findViewById(R.id.webview_hrv);
                if (videoWrapper != null) {
                    videoWrapper.performClick();
                    Log.i(TAG, "simulateClick: VideoWrapper clicked");
                    videoWrapper.post(() -> videoWrapper.invalidate());
                }
            });
        }
    }
}
package life.w1.pingpang.activity;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.Messenger;
import android.print.PrintAttributes;
import android.print.PrintDocumentAdapter;
import android.print.PrintManager;
import android.provider.Settings;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.fasterxml.jackson.core.type.TypeReference;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.util.Map;
import java.util.Optional;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import life.w1.pingpang.R;
import life.w1.pingpang.model.dto.ScreeningDetailDto;
import life.w1.pingpang.model.dto.ScreeningInfoDto;
import life.w1.pingpang.model.request.CreateScreeningRequest;
import life.w1.pingpang.model.request.SaveScreeningDetailRequest;
import life.w1.pingpang.model.response.ResponseEntity;
import life.w1.pingpang.service.ApiService;
import life.w1.pingpang.utils.BaseWebAppInterface;
import life.w1.pingpang.utils.ConfigUtils;
import life.w1.pingpang.utils.JsonUtils;

public class EyeActivity extends AppCompatActivity {

    private static final String TAG = "EyeActivity";

    // GazeConstants - 消息常量
    private static final int MSG_GAZE_TRACKING_STATE = 20001;
    private static final int MSG_SERVICE_CONNECTED = 10000;
    private static final int MSG_TURN_ON_CAMERA = 10020;
    private static final int MSG_START_TRACK = 10001;
    private static final int MSG_TURN_OFF_CAMERA = 10021;  // 关闭相机
    private static final int MSG_STOP_APPLIED = 10023;     // 停止眼动应用
    private static final String KEY_STATE = "state";

    private String deviceId;

    private String[] screeningUrls;

    private int index;

    private WebView webView;

    private JavaWebSocketClient client;

    private ScreeningInfoDto screeningInfo;

    // 新增的服务通信相关字段
    private Messenger mServiceManager;
    private Messenger mClientMessage;
    private boolean isGazeTrackingActive = false;

    // Handler for processing messages from service
    private final Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            parseMessage(msg);
        }
    };

    @SuppressLint({"SetJavaScriptEnabled", "HardwareIds"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_eye);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.eye_track), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        this.deviceId = Settings.Secure.getString(this.getContentResolver(), Settings.Secure.ANDROID_ID);

        // 初始化Messenger
        this.mClientMessage = new Messenger(handler);

        SharedPreferences prefs = getSharedPreferences("config_data", Context.MODE_PRIVATE);
        String eyemovement_mode = prefs.getString("eyemovement_mode", "");
        if (eyemovement_mode.equals("demo")) {
            this.screeningUrls = new String[]{
                    "https://llm.ybbywb.com/eyemovement2/test4.html",
                    "https://llm.ybbywb.com/eyemovement2/test5.html",
                    "https://llm.ybbywb.com/eyemovement2/test8.html",
            };
        } else {
            this.screeningUrls = new String[]{
                    "https://llm.ybbywb.com/eyemovement2/test1.html",
                    "https://llm.ybbywb.com/eyemovement2/test2.html",
                    "https://llm.ybbywb.com/eyemovement2/test3.html",
                    "https://llm.ybbywb.com/eyemovement2/test4.html",
                    "https://llm.ybbywb.com/eyemovement2/test5.html",
                    "https://llm.ybbywb.com/eyemovement2/test6.html",
//                    "https://llm.ybbywb.com/eyemovement2/test7.html",
//                    "https://llm.ybbywb.com/eyemovement2/test8.html",
            };
        }

        this.index = 0;

        this.webView = this.findViewById(R.id.webview_eye);
        this.webView.addJavascriptInterface(new JavaScriptInterface(this), "android");
        this.webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                view.loadUrl(request.getUrl().toString());
                return true;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
            }
        });

        WebSettings webSettings = this.webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        this.webView.clearCache(true);

        ApiService apiService = ApiService.getInstance();
        apiService.token = ConfigUtils.getUserToken(this);
        apiService.createScreening(CreateScreeningRequest.builder()
                        .profileId("test")
                        .category("EM")
                        .deviceId(this.deviceId)
                        .build())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<ResponseEntity<ScreeningInfoDto>>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        Log.d("createScreening", "onSubscribe");
                    }

                    @Override
                    public void onNext(@NonNull ResponseEntity<ScreeningInfoDto> response) {
                        Log.d("createScreening", "onNext: " + response);
                        EyeActivity.this.screeningInfo = response.getData();
                        EyeActivity.this.startScreening();
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        Log.d("createScreening", "onError: " + e);
                        Toast.makeText(EyeActivity.this, "Error: " + e, Toast.LENGTH_LONG).show();
                        EyeActivity.this.onClose();
                    }

                    @Override
                    public void onComplete() {
                        Log.d("createScreening", "onComplete");
                    }
                });

        // 启动外部眼动追踪前台服务（com.mitdd.gazetracker包中的服务）
        Intent serviceIntent = new Intent();
        serviceIntent.setComponent(new ComponentName("com.mitdd.gazetracker", "com.mitdd.gazetracker.gaze.track.GazeTrackService"));
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent);
                Log.d(TAG, "已启动外部眼动追踪前台服务 (API 26+)");
            } else {
                startService(serviceIntent);
                Log.d(TAG, "已启动外部眼动追踪服务 (API < 26)");
            }
        } catch (Exception e) {
            Log.e(TAG, "启动外部眼动追踪服务失败", e);
        }

        // 绑定外部服务
        Intent bindIntent = new Intent();
        bindIntent.setComponent(new ComponentName("com.mitdd.gazetracker", "com.mitdd.gazetracker.gaze.track.GazeTrackService"));
        try {
            bindService(bindIntent, serviceConnection, Context.BIND_AUTO_CREATE);
            Log.d(TAG, "正在绑定外部眼动追踪服务");
        } catch (Exception e) {
            Log.e(TAG, "绑定外部眼动追踪服务失败", e);
        }
    }

    // 解析从服务接收到的消息
    private void parseMessage(Message msg) {
        switch (msg.what) {
            case MSG_GAZE_TRACKING_STATE:
                boolean state = msg.getData().getBoolean(KEY_STATE);
                Log.d(TAG, "收到眼动追踪状态更新: " + state);

                if (state) {
                    // 眼动追踪启动成功
                    isGazeTrackingActive = true;
                    Log.i(TAG, "✓ 眼动追踪启动成功确认");
                } else {
                    // 眼动追踪停止
                    isGazeTrackingActive = false;
                    Log.i(TAG, "✓ 眼动追踪停止确认");
                }
                break;
            default:
                Log.d(TAG, "收到未处理的消息: " + msg.what);
                break;
        }
    }

    // 服务连接
    private final ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "EyeActivity onServiceConnected to: " + name.getPackageName());
            if (service != null) {
                mServiceManager = new Messenger(service);
                Message message = Message.obtain();
                message.what = MSG_SERVICE_CONNECTED;
                message.replyTo = mClientMessage;
                try {
                    mServiceManager.send(message);
                    Log.d(TAG, "✓ 已发送服务连接消息");
                } catch (Exception e) {
                    Log.e(TAG, "发送服务连接消息失败", e);
                }

                // 服务连接成功后立即发送开启相机消息
                Log.d(TAG, "📷 服务连接成功，发送开启相机消息");
                Message cameraMessage = Message.obtain();
                cameraMessage.what = MSG_TURN_ON_CAMERA;
                cameraMessage.replyTo = mClientMessage;
                try {
                    mServiceManager.send(cameraMessage);
                    Log.d(TAG, "✓ 已发送开启相机消息");
                } catch (Exception e) {
                    Log.e(TAG, "发送开启相机消息失败", e);
                }
            } else {
                Log.e(TAG, "服务连接成功但IBinder为null");
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "EyeActivity onServiceDisconnected from: " + name.getPackageName());
            mServiceManager = null;
            isGazeTrackingActive = false;
        }

        @Override
        public void onBindingDied(ComponentName name) {
            Log.e(TAG, "EyeActivity onBindingDied: " + name.getPackageName());
            mServiceManager = null;
            isGazeTrackingActive = false;
        }

        @Override
        public void onNullBinding(ComponentName name) {
            Log.e(TAG, "EyeActivity onNullBinding: " + name.getPackageName());
            mServiceManager = null;
        }
    };

    private void startScreening() {
        // 检查服务连接状态
        if (mServiceManager == null) {
            Log.e(TAG, "服务未连接，无法启动眼动追踪");
            webView.evaluateJavascript("javascript:onGazeTrackingStarted(false)", null);
            return;
        }

        // 1. 先发送开启相机消息
        Message cameraMessage = Message.obtain();
        cameraMessage.what = MSG_TURN_ON_CAMERA;
        cameraMessage.replyTo = mClientMessage;

        try {
            mServiceManager.send(cameraMessage);
            Log.d(TAG, "已发送开启相机消息");
        } catch (Exception e) {
            Log.e(TAG, "发送开启相机消息失败", e);
        }

        // 2. 发送启动追踪消息
        Message trackMessage = Message.obtain();
        trackMessage.what = MSG_START_TRACK;
        trackMessage.replyTo = mClientMessage;

        try {
            mServiceManager.send(trackMessage);
            Log.d(TAG, "已发送启动追踪消息");
        } catch (Exception e) {
            Log.e(TAG, "发送启动追踪消息失败", e);
        }

        // 保留原有的广播方式作为备用
        Intent intent = new Intent("com.mitdd.gazetracker.START_UP_GAZE_TRACK");
        intent.setPackage("com.mitdd.gazetracker");
        this.sendBroadcast(intent);

        this.runOnUiThread(() -> this.webView.loadUrl(this.screeningUrls[this.index]));
        new Handler().postDelayed(() -> {
            this.client = new JavaWebSocketClient(URI.create("ws://127.0.0.1:9200/Tracker"));
            this.client.connect();
        }, 2000);
    }

    private void onFinish() {
        Log.d(TAG, "onFinish - 筛查完成，开始释放资源");

        // 关闭WebSocket客户端
        if (this.client != null) {
            this.client.close();
        }

        // 通过Messenger发送关闭消息释放资源
        if (mServiceManager != null) {
            try {
                // 1. 发送关闭相机消息
                Message turnOffCameraMessage = Message.obtain();
                turnOffCameraMessage.what = MSG_TURN_OFF_CAMERA;
                turnOffCameraMessage.replyTo = mClientMessage;
                mServiceManager.send(turnOffCameraMessage);
                Log.d(TAG, "✓ 已发送关闭相机消息");

                // 2. 发送停止眼动应用消息
                Message stopAppMessage = Message.obtain();
                stopAppMessage.what = MSG_STOP_APPLIED;
                stopAppMessage.replyTo = mClientMessage;
                mServiceManager.send(stopAppMessage);
                Log.d(TAG, "✓ 已发送停止眼动应用消息");

            } catch (Exception e) {
                Log.e(TAG, "发送关闭消息失败", e);
            }
        }

        // 保留原有的广播方式作为备用
        Intent intent = new Intent("com.mitdd.gazetracker.SHUT_DOWN_GAZE_TRACK");
        intent.setPackage("com.mitdd.gazetracker");
        this.sendBroadcast(intent);

        // 停止外部眼动追踪服务
        try {
            Intent stopServiceIntent = new Intent();
            stopServiceIntent.setComponent(new ComponentName("com.mitdd.gazetracker",
                "com.mitdd.gazetracker.gaze.track.GazeTrackService"));
            stopService(stopServiceIntent);
            Log.d(TAG, "✓ 已停止外部眼动追踪服务");
        } catch (Exception e) {
            Log.e(TAG, "停止外部眼动追踪服务失败", e);
        }

        // 重置眼动追踪状态
        isGazeTrackingActive = false;

        if (!ConfigUtils.isHiddenReport(this).equals(1)) {
            String url = String.format("https://llm.ybbywb.com/eyemovement2/report.html?screeningId=%s", this.screeningInfo.getId());
            this.runOnUiThread(() -> this.webView.loadUrl(url));
        } else {
            this.finish();
        }
    }

    private void onClose() {
        Log.d(TAG, "onClose - 用户关闭，开始释放资源");

        // 通过Messenger发送关闭消息释放资源
        if (mServiceManager != null) {
            try {
                // 1. 发送关闭相机消息
                Message turnOffCameraMessage = Message.obtain();
                turnOffCameraMessage.what = MSG_TURN_OFF_CAMERA;
                turnOffCameraMessage.replyTo = mClientMessage;
                mServiceManager.send(turnOffCameraMessage);
                Log.d(TAG, "✓ 已发送关闭相机消息");

                // 2. 发送停止眼动应用消息
                Message stopAppMessage = Message.obtain();
                stopAppMessage.what = MSG_STOP_APPLIED;
                stopAppMessage.replyTo = mClientMessage;
                mServiceManager.send(stopAppMessage);
                Log.d(TAG, "✓ 已发送停止眼动应用消息");

            } catch (Exception e) {
                Log.e(TAG, "发送关闭消息失败", e);
            }
        }

        // 停止外部眼动追踪服务
        try {
            Intent stopServiceIntent = new Intent();
            stopServiceIntent.setComponent(new ComponentName("com.mitdd.gazetracker",
                "com.mitdd.gazetracker.gaze.track.GazeTrackService"));
            stopService(stopServiceIntent);
            Log.d(TAG, "✓ 已停止外部眼动追踪服务");
        } catch (Exception e) {
            Log.e(TAG, "停止外部眼动追踪服务失败", e);
        }

        // 重置眼动追踪状态
        isGazeTrackingActive = false;

        this.finish();
    }

    private void createWebPrintJob(WebView webView) {
        PrintManager printManager = (PrintManager) getSystemService(PRINT_SERVICE);
        String jobName = getString(R.string.app_name) + " Document";

        PrintDocumentAdapter printAdapter = webView.createPrintDocumentAdapter(jobName);

        printManager.print(jobName, printAdapter,
                new PrintAttributes.Builder()
                        .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                        .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                        .setResolution(new PrintAttributes.Resolution("pdf", "pdf", 600, 600))
                        .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                        .build());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "EyeActivity onDestroy - 开始释放资源");

        // 通过Messenger发送关闭消息释放资源
        if (mServiceManager != null) {
            try {
                // 1. 发送关闭相机消息
                Message turnOffCameraMessage = Message.obtain();
                turnOffCameraMessage.what = MSG_TURN_OFF_CAMERA;
                turnOffCameraMessage.replyTo = mClientMessage;
                mServiceManager.send(turnOffCameraMessage);
                Log.d(TAG, "✓ 已发送关闭相机消息");

                // 2. 发送停止眼动应用消息
                Message stopAppMessage = Message.obtain();
                stopAppMessage.what = MSG_STOP_APPLIED;
                stopAppMessage.replyTo = mClientMessage;
                mServiceManager.send(stopAppMessage);
                Log.d(TAG, "✓ 已发送停止眼动应用消息");

            } catch (Exception e) {
                Log.e(TAG, "发送关闭消息失败", e);
            }
        } else {
            Log.w(TAG, "Messenger服务未连接，无法发送关闭消息");
        }

        // 保留原有的广播方式作为备用
        Intent intent = new Intent("com.mitdd.gazetracker.SHUT_DOWN_GAZE_TRACK");
        intent.setPackage("com.mitdd.gazetracker");
        this.sendBroadcast(intent);
        Log.d(TAG, "✓ 已发送广播关闭消息");

        // 解绑服务
        try {
            unbindService(serviceConnection);
            Log.d(TAG, "✓ 已解绑服务");
        } catch (Exception e) {
            Log.e(TAG, "解绑服务失败", e);
        }

        // 停止外部眼动追踪服务
        try {
            Intent stopServiceIntent = new Intent();
            stopServiceIntent.setComponent(new ComponentName("com.mitdd.gazetracker",
                "com.mitdd.gazetracker.gaze.track.GazeTrackService"));
            stopService(stopServiceIntent);
            Log.d(TAG, "✓ 已停止外部眼动追踪服务");
        } catch (Exception e) {
            Log.e(TAG, "停止外部眼动追踪服务失败", e);
        }

        // 重置状态
        mServiceManager = null;
        isGazeTrackingActive = false;

        Log.d(TAG, "EyeActivity onDestroy - 资源释放完成");
    }

    private class JavaWebSocketClient extends WebSocketClient {

        public JavaWebSocketClient(URI serverUri) {
            super(serverUri);
        }

        @Override
        public void onOpen(ServerHandshake data) {
            Log.d("JavaWebSocketClient", "onOpen");
        }

        @Override
        public void onMessage(String message) {
            Log.d("JavaWebSocketClient", "onMessage: " + message);
            EyeActivity.this.runOnUiThread(() -> EyeActivity.this.webView.evaluateJavascript(String.format("javascript:track(%s)", message), null));
        }

        @Override
        public void onClose(int code, String reason, boolean remote) {
            Log.d("JavaWebSocketClient", "onClose");
        }

        @Override
        public void onError(Exception ex) {
            Log.d("JavaWebSocketClient", "onError");
        }

    }

    private class JavaScriptInterface extends BaseWebAppInterface {

        protected JavaScriptInterface(Context context) {
            super(context);
        }

        @JavascriptInterface
        public void onFinish(String data) {
            Map<String, Object> meta = JsonUtils.fromJson(data, new TypeReference<Map<String, Object>>() {});
            ApiService apiService = ApiService.getInstance();
            apiService.token = ConfigUtils.getUserToken(EyeActivity.this);
            apiService.saveScreeningDetail(SaveScreeningDetailRequest.builder()
                            .screeningId(Optional.ofNullable(EyeActivity.this.screeningInfo)
                                    .map(ScreeningInfoDto::getId)
                                    .orElse(""))
                            .name(Optional.ofNullable(meta)
                                    .filter(f -> f.containsKey("name"))
                                    .map(m -> m.get("name"))
                                    .map(Object::toString)
                                    .orElse(""))
                            .version("V1")
                            .detail(data)
                            .deviceId(EyeActivity.this.deviceId)
                            .build())
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Observer<ResponseEntity<ScreeningDetailDto>>() {
                        @Override
                        public void onSubscribe(@NonNull Disposable d) {
                            Log.d("saveScreeningDetail", "onSubscribe");
                        }

                        @Override
                        public void onNext(@NonNull ResponseEntity<ScreeningDetailDto> response) {
                            Log.d("saveScreeningDetail", "onNext: " + response);
                        }

                        @Override
                        public void onError(@NonNull Throwable e) {
                            Log.d("saveScreeningDetail", "onError: " + e);
                        }

                        @Override
                        public void onComplete() {
                            Log.d("saveScreeningDetail", "onComplete");
                        }
                    });

            EyeActivity.this.index++;
            if (EyeActivity.this.index >= EyeActivity.this.screeningUrls.length) {
                EyeActivity.this.onFinish();
                return;
            }

            EyeActivity.this.runOnUiThread(() -> EyeActivity.this.webView.loadUrl(EyeActivity.this.screeningUrls[EyeActivity.this.index]));
        }

        @JavascriptInterface
        public void printPage() {
            ((EyeActivity)context).runOnUiThread(() -> {
                WebView webView = ((EyeActivity)context).findViewById(R.id.webview_eye);
                ((EyeActivity)context).createWebPrintJob(webView);
            });
        }

        @JavascriptInterface
        public void onClose() {
            EyeActivity.this.onClose();
        }

    }

}
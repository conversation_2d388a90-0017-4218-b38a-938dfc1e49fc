package life.w1.pingpang.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.List;

public class JsonUtils {

    private static final ObjectMapper mapper = new ObjectMapper();

    public static <T> T fromJson(String json, Class<T> valueType) {
        try {
            return mapper.readValue(json, valueType);
        } catch (Exception ignored) {

        }
        return null;
    }

    public static <T> T fromJson(String json, TypeReference<T> valueTypeRef) {
        try {
            return mapper.readValue(json, valueTypeRef);
        } catch (Exception ignored) {

        }
        return null;
    }

    public static <T> List<T> fromJsonList(String json, Class<T> valueType) {
        try {
            JavaType javaType = mapper.getTypeFactory().constructCollectionType(List.class, valueType);
            return mapper.readValue(json, javaType);
        } catch (Exception ignored) {

        }
        return null;
    }

    public static <T> T fromJsonFile(File file, Class<T> valueType) {
        try {
            return mapper.readValue(file, valueType);
        } catch (Exception ignored) {

        }
        return null;
    }

    public static <T> T fromJsonFile(File file, TypeReference<T> valueTypeRef) {
        try {
            return mapper.readValue(file, valueTypeRef);
        } catch (Exception ignored) {

        }
        return null;
    }

    public static <T> List<T> fromJsonListFile(File file, Class<T> valueType) {
        try {
            JavaType javaType = mapper.getTypeFactory().constructCollectionType(List.class, valueType);
            return mapper.readValue(file, javaType);
        } catch (Exception ignored) {

        }
        return null;
    }

    public static String toJson(Object obj) {
        try {
            return mapper.writeValueAsString(obj);
        } catch (Exception ignored) {

        }
        return "";
    }

}

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#17d7e2"
    tools:context=".activity.LoginActivity">

    <View
        android:id="@+id/view_config"
        android:layout_width="268dp"
        android:layout_height="25dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="32dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/rounded_corner_amber"
        android:orientation="vertical"
        android:padding="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/tips_welcome_tsc102"
                android:textColor="@color/white"
                android:textSize="35sp" />
        </LinearLayout>
        <Space
            android:layout_width="match_parent"
            android:layout_height="32dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">
            <LinearLayout
                android:layout_width="260dp"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="300dp" />

                <ImageView
                    android:layout_width="108dp"
                    android:layout_height="108dp"
                    android:scaleType="fitCenter"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/logo_00" />

                <TextView
                    android:id="@+id/tvVersion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/tvDevice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="10sp" />
            </LinearLayout>
            <Space
                android:layout_width="160dp"
                android:layout_height="match_parent" />
            <LinearLayout
                android:layout_width="300dp"
                android:layout_height="match_parent"
                android:orientation="vertical">
                <!-- 姓名 -->
                <EditText
                    android:id="@+id/nameEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/nickname"
                    android:textColorHint="#000"
                    android:textSize="16sp"
                    android:paddingLeft="16dp"
                    android:inputType="textPersonName"
                    android:background="@drawable/edit_text_background"/>

                <!-- 性别 -->
                <RadioGroup
                    android:id="@+id/genderRadioGroup"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="8dp">

                    <RadioButton
                        android:id="@+id/maleRadioButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/gender_male"
                        android:checked="true"
                        android:layout_marginEnd="16dp"/>

                    <RadioButton
                        android:id="@+id/femaleRadioButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/gender_female"/>
                </RadioGroup>

                <!-- 年龄 -->
                <Spinner
                    android:id="@+id/spinnerAge"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/edit_text_background"
                    android:textSize="16sp"
                    android:prompt="@string/age_prompt" />

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="12dp" />

                <!-- 编号 -->
                <EditText
                    android:id="@+id/idnumEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/idnum"
                    android:textColorHint="#000"
                    android:textSize="16sp"
                    android:paddingLeft="16dp"
                    android:inputType="number"
                    android:background="@drawable/edit_text_background"/>

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="12dp" />


                <Spinner
                    android:id="@+id/spinnerGrade"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:prompt="@string/grade_prompt"
                    android:background="@drawable/edit_text_background"
                    android:layout_marginTop="16dp"/>

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="12dp" />


                <Spinner
                    android:id="@+id/spinnerClass"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:prompt="@string/class_prompt"
                    android:background="@drawable/edit_text_background"
                    android:layout_marginTop="16dp"/>


                <Space
                    android:layout_width="match_parent"
                    android:layout_height="56dp" />

                <Button
                    android:id="@+id/btn_main_survey"
                    android:layout_width="200dp"
                    android:layout_height="64dp"
                    android:backgroundTint="@color/btn_color"
                    android:onClick="showPanel"
                    android:layout_gravity="center"
                    android:text="@string/start_screening"
                    android:textColor="@color/white"
                    android:textSize="24sp" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; fill: #666; }
      .component { font-family: Arial, sans-serif; font-size: 12px; fill: #333; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #555; }
      .app-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .service-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .config-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .data-box { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #d32f2f; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="30" text-anchor="middle" class="title">眼动算法集成架构图</text>
  <text x="400" y="50" text-anchor="middle" class="subtitle">PingPang App - Eye Tracking Integration</text>
  
  <!-- 主应用区域 -->
  <rect x="50" y="80" width="300" height="200" rx="10" class="app-box"/>
  <text x="200" y="100" text-anchor="middle" class="subtitle">PingPang App</text>
  
  <!-- EyeActivity -->
  <rect x="70" y="120" width="100" height="40" rx="5" fill="#bbdefb" stroke="#1976d2"/>
  <text x="120" y="135" text-anchor="middle" class="component">EyeActivity</text>
  <text x="120" y="150" text-anchor="middle" class="small-text">主控制器</text>
  
  <!-- WebView -->
  <rect x="190" y="120" width="80" height="40" rx="5" fill="#bbdefb" stroke="#1976d2"/>
  <text x="230" y="135" text-anchor="middle" class="component">WebView</text>
  <text x="230" y="150" text-anchor="middle" class="small-text">界面渲染</text>
  
  <!-- WebSocket Client -->
  <rect x="70" y="180" width="100" height="40" rx="5" fill="#bbdefb" stroke="#1976d2"/>
  <text x="120" y="195" text-anchor="middle" class="component">WebSocket</text>
  <text x="120" y="210" text-anchor="middle" class="small-text">数据接收</text>
  
  <!-- JavaScript Interface -->
  <rect x="190" y="180" width="80" height="40" rx="5" fill="#bbdefb" stroke="#1976d2"/>
  <text x="230" y="195" text-anchor="middle" class="component">JS Interface</text>
  <text x="230" y="210" text-anchor="middle" class="small-text">数据处理</text>
  
  <!-- 外部服务区域 -->
  <rect x="400" y="80" width="300" height="200" rx="10" class="service-box"/>
  <text x="550" y="100" text-anchor="middle" class="subtitle">外部服务</text>
  
  <!-- 眼动追踪器 -->
  <rect x="420" y="120" width="120" height="50" rx="5" fill="#e1bee7" stroke="#7b1fa2"/>
  <text x="480" y="135" text-anchor="middle" class="component">GazeTracker</text>
  <text x="480" y="150" text-anchor="middle" class="small-text">com.mitdd.gazetracker</text>
  <text x="480" y="165" text-anchor="middle" class="small-text">眼动追踪算法</text>
  
  <!-- WebSocket Server -->
  <rect x="560" y="120" width="120" height="50" rx="5" fill="#e1bee7" stroke="#7b1fa2"/>
  <text x="620" y="135" text-anchor="middle" class="component">WebSocket Server</text>
  <text x="620" y="150" text-anchor="middle" class="small-text">127.0.0.1:9200</text>
  <text x="620" y="165" text-anchor="middle" class="small-text">数据传输</text>
  
  <!-- 后端API -->
  <rect x="490" y="200" width="120" height="50" rx="5" fill="#e1bee7" stroke="#7b1fa2"/>
  <text x="550" y="215" text-anchor="middle" class="component">后端API</text>
  <text x="550" y="230" text-anchor="middle" class="small-text">数据存储服务</text>
  <text x="550" y="245" text-anchor="middle" class="small-text">报告生成</text>
  
  <!-- 配置与权限区域 -->
  <rect x="50" y="320" width="650" height="120" rx="10" class="config-box"/>
  <text x="375" y="340" text-anchor="middle" class="subtitle">配置与权限</text>
  
  <!-- 权限配置 -->
  <rect x="80" y="360" width="150" height="60" rx="5" fill="#ffe0b2" stroke="#f57c00"/>
  <text x="155" y="380" text-anchor="middle" class="component">权限配置</text>
  <text x="155" y="395" text-anchor="middle" class="small-text">GAZE_TRACK</text>
  <text x="155" y="410" text-anchor="middle" class="small-text">CAMERA</text>
  
  <!-- 测试模式 -->
  <rect x="260" y="360" width="150" height="60" rx="5" fill="#ffe0b2" stroke="#f57c00"/>
  <text x="335" y="380" text-anchor="middle" class="component">测试模式</text>
  <text x="335" y="395" text-anchor="middle" class="small-text">Demo: 3个测试</text>
  <text x="335" y="410" text-anchor="middle" class="small-text">完整: 8个测试</text>
  
  <!-- 数据流程 -->
  <rect x="440" y="360" width="150" height="60" rx="5" fill="#ffe0b2" stroke="#f57c00"/>
  <text x="515" y="380" text-anchor="middle" class="component">数据流程</text>
  <text x="515" y="395" text-anchor="middle" class="small-text">采集→处理→存储</text>
  <text x="515" y="410" text-anchor="middle" class="small-text">→报告生成</text>
  
  <!-- 测试页面 -->
  <rect x="50" y="480" width="650" height="80" rx="10" class="data-box"/>
  <text x="375" y="500" text-anchor="middle" class="subtitle">眼动测试页面</text>
  <text x="375" y="520" text-anchor="middle" class="component">test1.html - test8.html</text>
  <text x="375" y="535" text-anchor="middle" class="small-text">https://llm.ybbywb.com/eyemovement2/</text>
  <text x="375" y="550" text-anchor="middle" class="small-text">眼动测试任务 + 实时数据可视化</text>
  
  <!-- 连接线 -->
  <!-- EyeActivity 到 WebView -->
  <line x1="170" y1="140" x2="190" y2="140" class="arrow"/>
  
  <!-- EyeActivity 到 WebSocket -->
  <line x1="120" y1="160" x2="120" y2="180" class="arrow"/>
  
  <!-- WebView 到 JS Interface -->
  <line x1="230" y1="160" x2="230" y2="180" class="arrow"/>
  
  <!-- 到外部服务的连接 -->
  <line x1="170" y1="140" x2="420" y2="145" class="data-flow"/>
  <text x="295" y="135" class="small-text">广播启动</text>
  
  <!-- GazeTracker 到 WebSocket Server -->
  <line x1="540" y1="145" x2="560" y2="145" class="data-flow"/>
  
  <!-- WebSocket Server 到 WebSocket Client -->
  <line x1="560" y1="145" x2="170" y2="200" class="data-flow"/>
  <text x="365" y="165" class="small-text">实时眼动数据</text>
  
  <!-- JS Interface 到 后端API -->
  <line x1="270" y1="200" x2="490" y2="225" class="data-flow"/>
  <text x="380" y="210" class="small-text">保存数据</text>
  
  <!-- 配置到主应用 -->
  <line x1="155" y1="360" x2="120" y2="280" class="arrow"/>
  <line x1="335" y1="360" x2="200" y2="280" class="arrow"/>
  
  <!-- 测试页面到WebView -->
  <line x1="230" y1="480" x2="230" y2="160" class="data-flow"/>
  <text x="240" y="320" class="small-text">加载测试页面</text>
</svg>

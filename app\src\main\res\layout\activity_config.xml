<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#17d7e2"
    tools:context=".activity.ConfigActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="32dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/rounded_corner_amber"
        android:orientation="vertical"
        android:padding="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="应用配置"
            android:textColor="@color/white"
            android:textSize="35sp"
            android:layout_marginBottom="40dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="20dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="隐藏Evaluation模块功能"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <Switch
                android:id="@+id/switch_hide_evaluation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="false"
                />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="包括: Eye、Calibration、Real Expression"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginBottom="30dp"
            android:layout_marginStart="20dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="20dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="隐藏Training模块功能"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <Switch
                android:id="@+id/switch_hide_training"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                />


        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="包括: Training模块的所有功能"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginStart="20dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

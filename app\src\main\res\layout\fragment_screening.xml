<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".fragment.ScreeningFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:baselineAligned="false"
        android:orientation="horizontal"
        tools:ignore="UselessParent">


        <LinearLayout
            android:id="@+id/btn_screening_hrv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="108dp"
                android:layout_height="108dp"
                android:contentDescription="@string/btn_screening_hrv_text"
                android:scaleType="fitCenter"
                android:src="@drawable/hrv" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/btn_screening_hrv_text"
                android:textSize="20sp" />
        </LinearLayout>

<!--        <Space-->
<!--            android:layout_width="32dp"-->
<!--            android:layout_height="wrap_content" />-->

        <LinearLayout
            android:id="@+id/btn_screening_eye"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="108dp"
                android:layout_height="108dp"
                android:contentDescription="@string/btn_screening_eye_text"
                android:scaleType="fitCenter"
                android:src="@drawable/eye" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/btn_screening_eye_text"
                android:textSize="20sp" />
        </LinearLayout>

<!--        <Space-->
<!--            android:layout_width="32dp"-->
<!--            android:layout_height="wrap_content" />-->

        <LinearLayout
            android:id="@+id/btn_screening_speech"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="108dp"
                android:layout_height="108dp"
                android:contentDescription="@string/btn_screening_speech_text"
                android:scaleType="fitCenter"
                android:src="@drawable/speech" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/btn_screening_speech_text"
                android:textSize="20sp" />
        </LinearLayout>


    </LinearLayout>

    <Space
        android:layout_width="match_parent"
        android:layout_height="32dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:baselineAligned="false"
        android:orientation="horizontal"
        tools:ignore="UselessParent">

        <LinearLayout
            android:id="@+id/btn_screening_emotion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="108dp"
                android:layout_height="108dp"
                android:contentDescription="@string/btn_screening_emotion_text"
                android:scaleType="fitCenter"
                android:src="@drawable/emotion" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/btn_screening_emotion_text"
                android:textSize="20sp" />
        </LinearLayout>

<!--        <Space-->
<!--            android:layout_width="32dp"-->
<!--            android:layout_height="wrap_content" />-->

        <LinearLayout
            android:id="@+id/btn_screening_expression_real_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="108dp"
                android:layout_height="108dp"
                android:contentDescription="@string/btn_screening_expression_real_time_text"
                android:scaleType="fitCenter"
                android:src="@drawable/emotion_real" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/btn_screening_expression_real_time_text"
                android:textSize="20sp" />
        </LinearLayout>

<!--        <Space-->
<!--            android:layout_width="32dp"-->
<!--            android:layout_height="wrap_content" />-->

        <LinearLayout
            android:id="@+id/btn_screening_calibration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="108dp"
                android:layout_height="108dp"
                android:contentDescription="@string/btn_screening_calibration_text"
                android:scaleType="fitCenter"
                android:src="@drawable/calibration" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/btn_screening_calibration_text"
                android:textSize="20sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_screening_other"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="108dp"
                android:layout_height="108dp"
                android:contentDescription="@string/btn_screening_calibration_text"
                android:scaleType="fitCenter"
                android:src="@drawable/calibration" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/btn_screening_calibration_text"
                android:textSize="20sp" />
        </LinearLayout>

    </LinearLayout>

</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#17d7e2"
    tools:context=".activity.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="32dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/rounded_corner_amber"
        android:orientation="horizontal"
        android:padding="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="256dp"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <Button
                android:id="@+id/btn_main_screening"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:backgroundTint="@color/btn_color"
                android:onClick="showPanel"
                android:text="@string/btn_main_screening_text"
                android:textColor="@color/white"
                android:textSize="24sp" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="32dp" />

            <Button
                android:id="@+id/btn_main_copilot"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:backgroundTint="@color/btn_color"
                android:onClick="showPanel"
                android:text="@string/btn_main_copilot_text"
                android:textColor="@color/white"
                android:textSize="24sp" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="32dp" />

            <Button
                android:id="@+id/btn_main_training"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:backgroundTint="@color/btn_color"
                android:onClick="showPanel"
                android:text="@string/btn_main_training_text"
                android:textColor="@color/white"
                android:textSize="24sp" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="32dp" />

            <Button
                android:id="@+id/btn_main_survey"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:backgroundTint="@color/btn_color"
                android:onClick="showPanel"
                android:text="@string/btn_main_survey_text"
                android:textColor="@color/white"
                android:textSize="24sp" />
            <Space
                android:layout_width="match_parent"
                android:layout_height="20dp" />

            <TextView
                android:id="@+id/screeningNameText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=""
                android:textColor="@color/white"
                android:textSize="22sp" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="20dp" />

            <ImageView
                android:layout_width="108dp"
                android:layout_height="108dp"
                android:scaleType="fitCenter"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/logo_00" />

            <TextView
                android:id="@+id/tvVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:text=""
                android:textColor="@color/white"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/tvDevice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:text=""
                android:textColor="@color/white"
                android:textSize="10sp" />
        </LinearLayout>

        <Space
            android:layout_width="32dp"
            android:layout_height="match_parent" />

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/rounded_corner_white"
            android:padding="32dp">

        </FrameLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
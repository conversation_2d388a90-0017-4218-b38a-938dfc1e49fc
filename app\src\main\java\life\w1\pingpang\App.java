package life.w1.pingpang;

import android.app.Application;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.util.Log;

import java.util.Locale;

import life.w1.pingpang.utils.LocaleHelper;

public class App extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        // 初始化代码，例如设置全局变量、初始化库等

        // 如果没有设置过语言，默认设置为中文
        SharedPreferences prefs = getSharedPreferences("config_data", MODE_PRIVATE);
        if (!prefs.contains("language")) {
            prefs.edit().putString("language", "zh").apply();
        }

        String lang = LocaleHelper.getLocale(this);
        Locale locale = new Locale(lang);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.setLocale(locale);
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());

        Log.d("application", "app set language " + lang);
    }
}

package life.w1.pingpang.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;

import java.util.Locale;

public class LocaleHelper {

    public static void setLocale(Context context, String lang) {
        Locale locale = new Locale(lang);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.setLocale(locale);
        context.getResources().updateConfiguration(config, context.getResources().getDisplayMetrics());

    }

    public static String getLocale(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("config_data", Context.MODE_PRIVATE);
        return prefs.getString("language", "zh");
    }
}
package life.w1.pingpang.utils;

import android.os.SystemClock;
import android.view.View;

/**
 * FileName: MultiClickListener
 * Author by lilin,Date on 2025/4/15 9:59
 * PS: Not easy to write code, please indicate.
 */
public abstract class MultiClickListener implements View.OnClickListener {

    private int count;
    private long duration;
    //记录点击次数
    private long[] hits;

    public MultiClickListener() {
        this(5, 2000);
    }

    public MultiClickListener(int count, long duration) {
        this.count = count;
        this.duration = duration;
        this.hits = new long[count];
    }

    @Override
    public void onClick(View v) {
        // 将 hits 数组内所有元素左移一个位置
        System.arraycopy(hits, 1, hits, 0, hits.length - 1);
        // 获取当前系统已经启动的时间
        hits[hits.length - 1] = SystemClock.uptimeMillis();
        if (hits[0] >= (SystemClock.uptimeMillis() - duration)) {
            // 在有效时间内已经连续点击了 count 次，算一次有效点击
            onClickValid(v);
            // 将所有时间重置
            for (int i = 0; i < hits.length; i++) {
                hits[i] = 0;
            }
        }
    }

    //当连续多次点击有效时回调
    public abstract void onClickValid(View v);
}

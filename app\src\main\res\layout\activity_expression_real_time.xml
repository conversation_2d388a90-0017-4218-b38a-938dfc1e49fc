<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/expressionRealTimeView"
    android:hardwareAccelerated="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.ExpressionRealTimeActivity">

    <WebView
        android:id="@+id/expressionRealTimeWebView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/loading_spinner"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#f3f4f5"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Spinner -->
        <ProgressBar
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:layout_gravity="center" />

        <!-- Loading Text -->
        <TextView
            android:id="@+id/loading_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tips_waiting_text"
            android:textSize="22sp"
            android:textColor="#19191a"
            android:layout_gravity="center"
            android:layout_marginTop="16dp" />
    </LinearLayout>

<!--    <ImageView-->
<!--        android:id="@+id/close_button"-->
<!--        android:layout_width="48dp"-->
<!--        android:layout_height="48dp"-->
<!--        android:src="@drawable/ic_close"-->
<!--        android:contentDescription="Close"-->
<!--        android:padding="8dp"-->
<!--        android:background="?attr/selectableItemBackgroundBorderless"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        android:layout_margin="16dp" />-->
</androidx.constraintlayout.widget.ConstraintLayout>
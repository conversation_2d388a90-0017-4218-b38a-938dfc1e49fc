package life.w1.pingpang.fragment;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import androidx.annotation.Nullable;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.fragment.app.Fragment;

import life.w1.pingpang.R;
import life.w1.pingpang.activity.MainActivity;
import life.w1.pingpang.utils.ConfigUtils;

public class TrainingFragment extends Fragment {

    private ActivityResultLauncher<Intent> activityResultLauncher;

    public TrainingFragment() {
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化 activityResultLauncher
        activityResultLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    // 处理外部应用返回的结果
                    if (result.getResultCode() == Activity.RESULT_OK) {
                        Toast.makeText(requireContext(), "外部应用返回成功", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(requireContext(), "外部应用未返回", Toast.LENGTH_SHORT).show();
                    }
                }
        );
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_training, container, false);
        view.findViewById(R.id.btn_training_eye).setOnClickListener(this::launchTraining);

        // 根据配置隐藏training模块的功能
        if (ConfigUtils.isHideTrainingModules(view.getContext())) {
            view.findViewById(R.id.btn_training_eye).setVisibility(View.GONE);
        }

        return view;
    }

    public void launchTraining(View v) {
        Intent intent = new Intent();
        intent.setClassName("com.babyeye.pad6", "com.babyeye.pad6.MainActivity");
        startActivity(intent);
        if (intent != null) {
            // 启动外部应用并等待其结果
            activityResultLauncher.launch(intent);
        } else {
            // 提示外部应用未安装
            Toast.makeText(requireContext(), "外部应用未安装", Toast.LENGTH_LONG).show();
        }
    }
}
package life.w1.pingpang.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Bundle;
import android.print.PrintAttributes;
import android.print.PrintDocumentAdapter;
import android.print.PrintManager;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.PermissionRequest;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import life.w1.pingpang.R;
import life.w1.pingpang.utils.BaseWebAppInterface;
import life.w1.pingpang.utils.ConfigUtils;


public class SpeechActivity extends AppCompatActivity {

    private static final String TAG = "EmotionActivity";
    private WebView webView;
    private String BASE_URL;

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "onCreate, savedInstanceState=" + (savedInstanceState != null ? savedInstanceState.toString() : "null"));
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_speech);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.speechView), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        this.webView = this.findViewById(R.id.speechWebView);
        this.webView.setBackgroundColor(Color.TRANSPARENT);
        this.webView.addJavascriptInterface(new SpeechActivity.JavaScriptInterface(this), "android");
        this.webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                // 显示加载动画
                findViewById(R.id.loading_spinner).setVisibility(View.VISIBLE);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // 隐藏加载动画
                findViewById(R.id.loading_spinner).setVisibility(View.GONE);
            }
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                view.loadUrl(request.getUrl().toString());
                return true;
            }
        });
        this.webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onPermissionRequest(PermissionRequest request) {
                Log.i(TAG, "onPermissionRequest");
                SpeechActivity.this.runOnUiThread(() -> {
                    Log.i(TAG, "onPermissionRequest, runOnUiThread");
                    if (ContextCompat.checkSelfPermission(SpeechActivity.this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED) {
                        Log.i(TAG, "onPermissionRequest, grant");
                        request.grant(request.getResources());
                    } else {
                        Log.i(TAG, "onPermissionRequest, request");
                        ActivityCompat.requestPermissions(SpeechActivity.this, new String[]{Manifest.permission.RECORD_AUDIO}, 1);
                    }
                });
            }
        });

        WebSettings webSettings = this.webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        webSettings.setMediaPlaybackRequiresUserGesture(false);
        this.webView.clearCache(true);

        this.BASE_URL = ConfigUtils.getBaseUrl(this);

        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        if (ContextCompat.checkSelfPermission(SpeechActivity.this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED) {
            this.webView.loadUrl(this.BASE_URL + "/speech?device_id=" + androidId + "&lang=" + ConfigUtils.getLang(this) + "&token=" + ConfigUtils.getUserToken(this));
        } else {
            ActivityCompat.requestPermissions(SpeechActivity.this, new String[]{Manifest.permission.RECORD_AUDIO}, 1);
        }

//        ImageView closeButton = findViewById(R.id.close_button);
//        closeButton.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                // Close the current activity
//                finish();
//            }
//        });
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy");
        if (webView != null) {
            webView.removeAllViews();
            webView.destroy();
            webView = null;
        }
        super.onDestroy();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        if (requestCode == 1) {
            if (permissions.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                this.webView.loadUrl(this.BASE_URL + "/speech?device_id=" + androidId + "&lang=" + ConfigUtils.getLang(this) + "&token=" + ConfigUtils.getUserToken(this));
            } else {
                Toast.makeText(this, "No Permission", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void createWebPrintJob(WebView webView) {
        PrintManager printManager = (PrintManager) getSystemService(PRINT_SERVICE);
        String jobName = getString(R.string.app_name) + " Document";

        PrintDocumentAdapter printAdapter = webView.createPrintDocumentAdapter(jobName);

        printManager.print(jobName, printAdapter,
                new PrintAttributes.Builder()
                        .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                        .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                        .setResolution(new PrintAttributes.Resolution("pdf", "pdf", 600, 600))
                        .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                        .build());
    }

    private class JavaScriptInterface extends BaseWebAppInterface {

        public JavaScriptInterface(Context context) {
            super(context);
        }

        @JavascriptInterface
        public void onFinish() {
            runOnUiThread(() -> {
                Log.i(TAG, "onFinish");
                SpeechActivity.this.finish();
            });
        }

        @JavascriptInterface
        public void simulateClick() {
            runOnUiThread(() -> {
                // 模拟点击 VideoWrapper 的逻辑
                View videoWrapper = findViewById(R.id.webview_hrv);
                if (videoWrapper != null) {
                    videoWrapper.performClick();
                    Log.i(TAG, "simulateClick: VideoWrapper clicked");
                    videoWrapper.post(() -> videoWrapper.invalidate());
                }
            });
        }

        @JavascriptInterface
        public void printPage() {
            ((SpeechActivity)context).runOnUiThread(() -> {
                WebView webView = ((SpeechActivity)context).findViewById(R.id.speechWebView);
                ((SpeechActivity)context).createWebPrintJob(webView);
            });
        }
    }
}